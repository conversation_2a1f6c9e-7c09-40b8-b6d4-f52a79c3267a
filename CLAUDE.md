# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Build and Deployment
- **Build production package**: `./build.sh` - Creates optimized production deployment package with auto-configured environment settings
- **Build for local testing**: `./build.sh local` - Creates local binary for development/testing
- **Start service**: `./start.sh` (in deployment package)
- **Stop service**: `./stop.sh` (in deployment package)
- **View logs**: `./logs.sh app|error|access|audit` (in deployment package)

### Development
- **Run locally**: `go run cmd/main.go` - Starts server on port 8081
- **Test**: `go test ./...` - Run all tests
- **Build binary**: `go build -o kuaidi-server cmd/main.go`
- **Download dependencies**: `go mod download && go mod verify`

### Frontend Development
- **Admin frontend**: `cd admin-frontend && npm run dev`
- **User frontend**: `cd user-frontend && npm run dev`
- **Callback receiver**: `cd callback-receiver-service && go run cmd/main.go`

### Frontend Tools
- **Lint & format**: `npm run lint` and `npm run fix` (in frontend dirs)
- **Build frontend**: `npm run build` (TypeScript checking + Vite build)
- **Preview build**: `npm run serve` (serves production build locally)

## Architecture

### High-Level Structure
This is a microservices-based express delivery management system with clean architecture:

- **Main Go Backend** (`cmd/main.go`): Core logistics API server (port 8081)
- **Callback Receiver Service** (`callback-receiver-service/`): Separate microservice for provider callbacks
- **Admin Frontend** (`admin-frontend/`): Vue.js management dashboard
- **User Frontend** (`user-frontend/`): Vue.js customer interface

### Core Layers
1. **Handlers** (`api/handler/`): HTTP request/response handling, input validation
2. **Services** (`internal/service/`): Business logic, transaction management, orchestration
3. **Repositories** (`internal/repository/`): Data access, database operations
4. **Adapters** (`internal/adapter/`): External provider integration (Kuaidi100, Cainiao, Yida, etc.)
5. **Models** (`internal/model/`): Data structures and domain entities

### Key Patterns
- **Clean Architecture**: Strict dependency inversion with interfaces
- **Adapter Pattern**: Unified interface for multiple express delivery providers
- **Repository Pattern**: Database abstraction with transaction support
- **Factory Pattern**: Dynamic provider selection and configuration
- **Observer Pattern**: Event-driven callback processing

## Provider Integration System

### Supported Providers
- **Kuaidi100** (`internal/adapter/kuaidi100.go`): Industry standard API
- **Cainiao** (`internal/adapter/cainiao.go`): Alibaba logistics platform
- **Yida** (`internal/adapter/yida.go`): Regional express provider
- **Yuntong** (`internal/adapter/yuntong.go`): Cloud-based logistics
- **Kuaidiniao** (`internal/adapter/kuaidiniao.go`): Multi-carrier platform

### Integration Architecture
- **Unified Gateway**: Single API endpoint handling all providers
- **Dynamic Provider Manager**: Runtime provider switching and failover
- **Standardized Callbacks**: Common format for all provider status updates
- **Price Comparison**: Real-time multi-provider pricing

## Configuration

### Main Config (`config/config.yaml`)
- Database: PostgreSQL with connection pooling (200 connections)
- Redis: Caching and session management
- Providers: Database-driven configuration
- Security: JWT with RSA keys, signature validation
- Logging: Multi-level with file rotation

### Environment Auto-Configuration
Build script automatically replaces connection strings:
- Dev: `*************:5432` → Prod: `1Panel-postgresql-HioR:5432`
- Dev: `*************:6379` → Prod: `1Panel-redis-dryE:6379`

## Key Business Features

### Order Management
- **Multi-step Lifecycle**: Creation → Provider Selection → Submission → Tracking → Completion
- **Smart Provider Selection**: Algorithm-based optimal provider choice
- **Real-time Status Updates**: Callback-driven status synchronization
- **Platform Order Numbers**: Unified order numbering across providers

### Callback System
The callback system processes delivery status updates from providers:
- **Callback Router** (`api/router/callback_router.go`): Routes provider callbacks
- **Standardizer** (`internal/service/callback/standardizer.go`): Converts provider formats
- **Status Mapping** (`config/status_mapping.yaml`): Maps provider statuses to internal codes
- **Unified Service** (`internal/service/callback/unified_callback_service.go`): Central processing

### Price Management
- **Enhanced Price Service** (`internal/service/enhanced_price_service.go`): Multi-provider comparison
- **Price Validation** (`internal/service/order_price_validation_service.go`): Real-time validation
- **Dynamic Pricing**: Provider-specific rate calculations

## Development Guidelines

### Code Organization
- Follow clean architecture principles with clear layer separation
- Use dependency injection through constructors and interfaces
- Implement comprehensive error handling with custom error types
- Maintain transaction boundaries at service layer

### Database Patterns
- Use GORM with proper transaction management
- Implement soft deletes for audit trails
- Follow naming conventions: snake_case for database, camelCase for Go
- Use repository pattern for all database operations

### API Design
- RESTful endpoints with proper HTTP methods and status codes
- Consistent JSON response format with error handling
- JWT authentication with role-based access control
- Request validation at handler layer

### Testing Approach
- **Unit tests**: Individual component testing with `*_test.go` files
- **Integration tests**: Service interaction testing using table-driven tests
- **Mock interfaces**: Interface-based testing for repositories and external services
- **Test structure**: Follow Arrange-Act-Assert pattern with descriptive test names
- **Frontend tests**: Vitest for Vue components, Cypress for E2E testing

## Security Features

### Authentication & Authorization
- JWT tokens with RSA signature verification
- Request signature validation with nonce management
- Role-based access control (admin/user)
- Comprehensive audit logging

### Data Protection
- Input validation at all entry points
- SQL injection prevention through parameterized queries
- Rate limiting and request throttling
- Sensitive data encryption in transit and at rest

## Performance Optimizations

### Database
- Connection pooling with 200 max connections
- Query optimization with proper indexes
- Prepared statements for repeated queries
- Database operation monitoring

### Caching Strategy
- Redis for session management and API responses
- Application-level caching for static data
- Cache invalidation strategies for data consistency

### Monitoring
- Structured logging with Zap (JSON format)
- Prometheus metrics integration
- Performance monitoring for critical paths
- Error tracking and alerting

## Troubleshooting

### Common Issues
- **Port conflicts**: Ensure 8081 is available
- **Database connectivity**: Check PostgreSQL connection and credentials
- **Redis issues**: Verify Redis server status and connection
- **Provider API failures**: Check provider adapter configurations

### Debugging Tools
- **Debug script**: `./debug.sh` (in deployment package) - Comprehensive startup diagnostics
- **Log viewer**: `./logs.sh help` - Multi-category log management
- **Status checker**: `./status.sh` - Process and system status
- **Optimizer**: `./optimize.sh` - Performance analysis and recommendations

### Log Categories
- **Application logs**: `logs/go-kuaidi.log` - Main business logic
- **Error logs**: `logs/error.log` - Error tracking and debugging
- **Access logs**: `logs/access.log` - HTTP request/response logging  
- **Audit logs**: `logs/audit.log` - Security and compliance tracking

## Code Architecture Guidelines

### 📏 硬性指标（Must-Follow）

#### ✅ 文件行数限制
- **动态语言**（如 Python、JavaScript、TypeScript）：每个代码文件**不超过 200 行**
- **静态语言**（如 Java、Go、Rust）：每个代码文件**不超过 250 行**

#### ✅ 文件夹结构限制
- 每个文件夹中**文件数量不超过 8 个**，若超过需进行多层子文件夹拆分

### 🧠 架构设计关注点（持续警惕）

以下"坏味道"会严重侵蚀代码质量，必须时刻警惕并避免：

#### ❌ 避免的架构问题
- **僵化（Rigidity）**: 系统难以变更，微小改动引发连锁反应
- **冗余（Redundancy）**: 相同逻辑重复出现，维护困难
- **循环依赖（Circular Dependency）**: 模块相互依赖，形成"死结"
- **脆弱性（Fragility）**: 修改一处，导致看似无关部分出错
- **晦涩性（Obscurity）**: 代码结构混乱，意图不明
- **数据泥团（Data Clump）**: 多个参数总是一起出现，暗示应封装为对象
- **不必要的复杂性（Needless Complexity）**: 过度设计，小问题用大方案

#### 🚨 重要提醒
一旦发现"坏味道"，应立即提醒用户是否需要优化，并提供合理的优化建议。

## 测试和开发配置

### 测试账号
- **管理员账号**: admin
- **管理员密码**: 1104030777+.aA..@
- **测试令牌位置**: `/Users/<USER>/Desktop/go-kuaidi-7.4.00.21/admin-token.txt`
- **注意**: 管理员账号也可以登录普通用户登录接口

### 数据库配置
- **回调接收服务数据库**: `*************************************************/callback_receiver?sslmode=disable`
- **主服务数据库**: `*************************************************/go_kuaidi`

### 本地开发
- **启动后端脚本**: `/Users/<USER>/Desktop/go-kuaidi-7.4.00.21/start-local.sh`
- **交流语言**: 中文