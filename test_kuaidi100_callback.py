#!/usr/bin/env python3
"""
快递100回调模拟推送测试脚本
运单号: 312868721376618 (韵达快递)
"""

import requests
import json
import urllib.parse
import time

# 服务端点
CALLBACK_URL = "http://localhost:8081/api/v1/callbacks/kuaidi100"

# 原始回调数据 (状态=9，已签收)
callback_payload = {
    "param": json.dumps({
        "data": {
            "courierName": "柳亚",
            "defPrice": None,
            "mktId": None,
            "orderId": "301805114",
            "courierMobile": "18182006611",
            "price": None,
            "freight": None,
            "weight": None,
            "pollToken": "C80LnV3oK+8oOiu+ogAHa84VbY8li8Qxf0cR0qYE5Vc=",
            "thirdOrderId": "GK20250820000001497",
            "status": 9  # 已签收
        },
        "kuaidicom": "yunda",  # 韵达
        "kuaidinum": "312868721376618",  # 运单号
        "message": "成功",
        "status": "200"
    }, ensure_ascii=False),
    "sign": "DDE1B3E865274B8569738B13A74AE16D",
    "taskId": "6753640C8C4C227AC1E32ECC89F5A848"
}

def test_callback():
    print(f"🚀 开始测试快递100回调处理")
    print(f"📦 运单号: 312868721376618")
    print(f"🚚 快递公司: 韵达 (yunda)")
    print(f"📍 状态: 9 (已签收)")
    print(f"🔗 目标URL: {CALLBACK_URL}")
    print("-" * 50)
    
    try:
        # 发送POST请求
        print("📤 发送回调请求...")
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Mozilla/5.0 (compatible; Kuaidi100Bot/1.0)'
        }
        
        response = requests.post(
            CALLBACK_URL,
            data=callback_payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📨 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 回调处理成功")
        else:
            print(f"❌ 回调处理失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def test_different_statuses():
    """测试不同状态的回调"""
    statuses = [
        (0, "下单"),
        (1, "已发货"), 
        (2, "在途中"),
        (3, "派送中"),
        (9, "已签收")
    ]
    
    print("🧪 测试不同状态的回调处理...")
    
    for status, desc in statuses:
        print(f"\n📊 测试状态: {status} ({desc})")
        
        test_payload = callback_payload.copy()
        param_data = json.loads(test_payload["param"])
        param_data["data"]["status"] = status
        test_payload["param"] = json.dumps(param_data, ensure_ascii=False)
        
        try:
            response = requests.post(
                CALLBACK_URL,
                data=test_payload,
                headers={'Content-Type': 'application/x-www-form-urlencoded'},
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"   响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            
        time.sleep(0.5)  # 避免过于频繁的请求

if __name__ == "__main__":
    print("=" * 60)
    print("快递100回调模拟测试")
    print("=" * 60)
    
    # 测试主要回调
    test_callback()
    
    print("\n" + "=" * 60)
    
    # 测试不同状态
    test_different_statuses()
    
    print("\n✅ 测试完成")