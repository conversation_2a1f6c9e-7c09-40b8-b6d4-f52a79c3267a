#!/bin/bash

# 回调功能测试脚本
# 验证回调功能是否正常工作（内部处理 + 外部转发）

echo "🔍 回调功能测试开始..."

DB_URL="*************************************************/go_kuaidi"

echo ""
echo "1️⃣ 检查回调配置表结构..."
psql "$DB_URL" -c "
SELECT
    column_name,
    data_type
FROM information_schema.columns
WHERE table_name = 'user_callback_configs'
ORDER BY ordinal_position;
"

echo ""
echo "2️⃣ 检查用户回调配置..."
psql "$DB_URL" -c "
SELECT
    user_id,
    callback_url,
    enabled,
    retry_count,
    timeout_seconds,
    created_at
FROM user_callback_configs
ORDER BY created_at DESC
LIMIT 5;
"

echo ""
echo "3️⃣ 检查最近的回调记录..."
psql "$DB_URL" -c "
SELECT
    provider,
    user_id,
    order_no,
    event_type,
    internal_status,
    external_status,
    created_at
FROM unified_callback_records
ORDER BY created_at DESC
LIMIT 10;
"

echo ""
echo "4️⃣ 检查回调转发记录..."
psql "$DB_URL" -c "
SELECT
    callback_record_id,
    user_id,
    callback_url,
    status,
    http_status,
    created_at
FROM callback_forward_records
ORDER BY created_at DESC
LIMIT 10;
"

echo ""
echo "5️⃣ 统计回调处理情况..."
psql "$DB_URL" -c "
-- 回调记录统计
SELECT
    '回调记录统计' as info,
    provider,
    COUNT(*) as total_records,
    COUNT(CASE WHEN internal_status = 'success' THEN 1 END) as internal_success,
    COUNT(CASE WHEN external_status = 'success' THEN 1 END) as external_success,
    COUNT(CASE WHEN internal_status = 'failed' THEN 1 END) as internal_failed,
    COUNT(CASE WHEN external_status = 'failed' THEN 1 END) as external_failed
FROM unified_callback_records
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY provider
ORDER BY total_records DESC;

-- 转发记录统计
SELECT
    '转发记录统计' as info,
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM callback_forward_records
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY status
ORDER BY count DESC;
"

echo ""
echo "6️⃣ 检查系统配置..."
psql "$DB_URL" -c "
SELECT 
    config_group,
    config_key,
    config_value,
    description
FROM system_configs 
WHERE config_group = 'callback'
ORDER BY config_key;
"

echo ""
echo "✅ 回调功能测试完成！"
echo ""
echo "📋 测试结果说明："
echo "- 如果看到用户回调配置，说明用户可以正常配置回调"
echo "- 如果看到回调记录，说明系统正常接收和处理回调"
echo "- 如果看到转发记录，说明系统正常向用户转发回调"
echo "- 成功率应该在90%以上为正常"
