#!/bin/bash

# 回调订阅功能管理脚本
# 用法: ./manage_callback_subscription.sh [enable|disable|status]

DB_URL="***********************************************************"

function show_usage() {
    echo "用法: $0 [enable|disable|status]"
    echo ""
    echo "命令:"
    echo "  enable   - 启用回调订阅功能"
    echo "  disable  - 禁用回调订阅功能"
    echo "  status   - 查看当前状态"
    echo ""
    echo "示例:"
    echo "  $0 disable  # 禁用回调订阅功能"
    echo "  $0 enable   # 启用回调订阅功能"
    echo "  $0 status   # 查看当前状态"
}

function get_status() {
    echo "🔍 查询回调订阅功能状态..."
    psql "$DB_URL" -c "
        SELECT 
            config_group,
            config_key,
            config_value,
            CASE 
                WHEN config_value = 'true' THEN '✅ 已启用'
                WHEN config_value = 'false' THEN '❌ 已禁用'
                ELSE '❓ 未知状态'
            END as status,
            updated_at
        FROM system_configs 
        WHERE config_group = 'callback' AND config_key = 'subscription_enabled';
    "
}

function enable_subscription() {
    echo "🔄 启用回调订阅功能..."
    psql "$DB_URL" -c "
        UPDATE system_configs 
        SET 
            config_value = 'true',
            updated_at = NOW(),
            updated_by = 'admin'
        WHERE 
            config_group = 'callback' 
            AND config_key = 'subscription_enabled';
    "
    
    if [ $? -eq 0 ]; then
        echo "✅ 回调订阅功能已启用"
        get_status
    else
        echo "❌ 启用失败"
        exit 1
    fi
}

function disable_subscription() {
    echo "🔄 禁用回调订阅功能..."
    psql "$DB_URL" -c "
        UPDATE system_configs 
        SET 
            config_value = 'false',
            updated_at = NOW(),
            updated_by = 'admin'
        WHERE 
            config_group = 'callback' 
            AND config_key = 'subscription_enabled';
    "
    
    if [ $? -eq 0 ]; then
        echo "❌ 回调订阅功能已禁用"
        get_status
    else
        echo "❌ 禁用失败"
        exit 1
    fi
}

# 主逻辑
case "$1" in
    "enable")
        enable_subscription
        ;;
    "disable")
        disable_subscription
        ;;
    "status")
        get_status
        ;;
    *)
        show_usage
        exit 1
        ;;
esac
