package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/service"
)

func main() {
	// 初始化日志
	logger, _ := zap.NewDevelopment()
	defer logger.Sync()

	// 加载配置
	configManager := config.GetConfigManager()
	if err := configManager.Load(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	dbConnStr := "*************************************************/go_kuaidi?sslmode=disable&timezone=Asia/Shanghai"
	db, err := sql.Open("postgres", dbConnStr)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 创建系统配置服务
	systemConfigRepo := repository.NewPostgresSystemConfigRepository(db)
	systemConfigService := service.NewDefaultSystemConfigService(systemConfigRepo, logger)

	// 测试获取回调订阅功能开关
	ctx := context.Background()
	subscriptionEnabled, err := systemConfigService.GetBoolConfig(ctx, "callback", "subscription_enabled", true)
	if err != nil {
		log.Printf("获取回调订阅功能开关失败: %v", err)
	} else {
		fmt.Printf("回调订阅功能开关状态: %t\n", subscriptionEnabled)
	}

	// 测试从配置文件获取
	yamlEnabled := configManager.GetBool("callback.subscription_enabled")
	fmt.Printf("YAML配置中的回调订阅功能开关: %t\n", yamlEnabled)

	if subscriptionEnabled {
		fmt.Println("✅ 回调订阅功能已启用")
	} else {
		fmt.Println("❌ 回调订阅功能已禁用")
	}
}
