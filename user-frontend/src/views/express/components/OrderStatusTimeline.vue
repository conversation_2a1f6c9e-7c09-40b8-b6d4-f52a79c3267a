<template>
  <el-card class="status-timeline-card">
    <template #header>
      <div class="timeline-header">
        <span>订单状态历史</span>
        <div class="header-actions">
          <el-button type="text" @click="toggleStatistics" size="small">
            <el-icon><DataAnalysis /></el-icon>
            {{ showStatistics ? '隐藏' : '显示' }}统计
          </el-button>
          <el-button type="text" @click="refreshHistory" :loading="loading" size="small">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </template>

    <!-- 🔥 新增：状态统计信息 -->
    <div v-if="showStatistics && historyItems.length > 0" class="status-statistics">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ statusStatistics.totalChanges }}</div>
            <div class="stat-label">状态变更次数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ statusStatistics.totalDuration }}</div>
            <div class="stat-label">总耗时</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ statusStatistics.averageDuration }}</div>
            <div class="stat-label">平均状态持续</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ statusStatistics.exceptionCount }}</div>
            <div class="stat-label">异常状态次数</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div v-loading="loading" element-loading-text="正在加载状态历史...">
      <el-timeline v-if="historyItems.length > 0">
        <el-timeline-item
          v-for="(item, index) in historyItems"
          :key="item.id"
          :timestamp="formatTimelineDateTime(item.created_at)"
          :type="getTimelineType(item.change_source)"
          :icon="getStatusIcon(item.to_status)"
          placement="top"
        >
          <div class="status-change-item" :class="{ 'exception-status': isExceptionStatus(item.to_status) }">
            <div class="status-change-header">
              <el-tag
                :type="getStatusType(item.to_status)"
                :effect="isExceptionStatus(item.to_status) ? 'dark' : 'light'"
                size="small"
              >
                {{ item.to_status_desc }}
                <el-icon v-if="isExceptionStatus(item.to_status)" class="exception-icon">
                  <Warning />
                </el-icon>
              </el-tag>
              <span class="change-source">{{ item.change_source_desc }}</span>
            </div>

            <div class="status-change-details">
              <span class="status-transition">
                {{ item.from_status_desc || '初始状态' }} → {{ item.to_status_desc }}
              </span>
              <span v-if="item.duration" class="duration" :class="{ 'long-duration': isLongDuration(item.duration) }">
                持续时间：{{ item.duration }}
                <el-icon v-if="isLongDuration(item.duration)" class="duration-warning">
                  <Clock />
                </el-icon>
              </span>
            </div>

            <div v-if="item.operator_name" class="operator-info">
              <el-icon><User /></el-icon>
              操作人员：{{ item.operator_name }}
            </div>

            <div v-if="item.change_reason" class="change-reason">
              <el-icon><Document /></el-icon>
              变更原因：{{ item.change_reason }}
            </div>

            <!-- 🔥 新增：状态变更详细信息 -->
            <div v-if="getStatusDetails(item)" class="status-details">
              <el-collapse>
                <el-collapse-item title="详细信息" :name="item.id">
                  <div class="detail-content">
                    <p><strong>状态代码：</strong>{{ item.to_status }}</p>
                    <p><strong>供应商：</strong>{{ item.provider_name || item.provider }}</p>
                    <p v-if="item.created_at"><strong>变更时间：</strong>{{ formatFullDateTime(item.created_at) }}</p>
                    <p v-if="index < historyItems.length - 1"><strong>下次变更：</strong>{{ formatFullDateTime(historyItems[index + 1].created_at) }}</p>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>

      <el-empty v-else-if="!loading" description="暂无状态变更记录" />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'

import {
  Refresh,
  DataAnalysis,
  Warning,
  Clock,
  User,
  Document,
  CircleCheck,
  CircleClose,
  Loading,
  QuestionFilled
} from '@element-plus/icons-vue'
import { ExpressService } from '@/api/expressApi'
import type { StatusHistoryItem, OrderListItem } from '@/api/model/kuaidiModel'

interface Props {
  order: OrderListItem
  visible?: boolean
}

const props = defineProps<Props>()

// 响应式数据
const loading = ref(false)
const historyItems = ref<StatusHistoryItem[]>([])
const showStatistics = ref(false)

// 计算属性
const shouldLoad = computed(() => props.visible !== false && props.order)

// 🔥 新增：状态统计计算
const statusStatistics = computed(() => {
  if (historyItems.value.length === 0) {
    return {
      totalChanges: 0,
      totalDuration: '0分钟',
      averageDuration: '0分钟',
      exceptionCount: 0
    }
  }

  const items = historyItems.value
  const totalChanges = items.length
  let exceptionCount = 0
  let totalMinutes = 0
  let validDurations = 0

  items.forEach(item => {
    if (isExceptionStatus(item.to_status)) {
      exceptionCount++
    }

    if (item.duration && item.duration !== '至今') {
      const minutes = parseDurationToMinutes(item.duration)
      if (minutes > 0) {
        totalMinutes += minutes
        validDurations++
      }
    }
  })

  const averageMinutes = validDurations > 0 ? Math.round(totalMinutes / validDurations) : 0

  return {
    totalChanges,
    totalDuration: formatMinutesToDuration(totalMinutes),
    averageDuration: formatMinutesToDuration(averageMinutes),
    exceptionCount
  }
})

// 加载状态历史
const loadStatusHistory = async () => {
  if (!props.order) return

  loading.value = true
  try {
    const response = await ExpressService.getOrderStatusHistory({
      customer_order_no: props.order.customer_order_no,
      page: 1,
      limit: 50
    })

    if (response.success && response.data) {
      historyItems.value = response.data.items
    } else {
      throw new Error(response.message || '获取状态历史失败')
    }
  } catch (error) {
    console.error('获取状态历史失败:', error)
    ElMessage.error('获取状态历史失败')
    historyItems.value = []
  } finally {
    loading.value = false
  }
}

// 刷新状态历史
const refreshHistory = async () => {
  await loadStatusHistory()
  ElMessage.success('状态历史已刷新')
}

// 🔥 新增：切换统计显示
const toggleStatistics = () => {
  showStatistics.value = !showStatistics.value
}

// 🔥 新增：判断是否为异常状态
const isExceptionStatus = (status: string): boolean => {
  const exceptionStatuses = [
    'exception', 'delivery_failed', 'pickup_failed', 'submit_failed',
    'returned', 'cancelled', 'voided', 'delivered_abnormal'
  ]
  return exceptionStatuses.includes(status)
}

// 🔥 新增：判断是否为长时间持续状态
const isLongDuration = (duration: string): boolean => {
  if (!duration || duration === '至今') return false

  const minutes = parseDurationToMinutes(duration)
  return minutes > 24 * 60 // 超过24小时
}

// 🔥 新增：解析持续时间为分钟数
const parseDurationToMinutes = (duration: string): number => {
  if (!duration || duration === '至今') return 0

  let totalMinutes = 0

  // 匹配天数
  const dayMatch = duration.match(/(\d+)天/)
  if (dayMatch) {
    totalMinutes += parseInt(dayMatch[1]) * 24 * 60
  }

  // 匹配小时数
  const hourMatch = duration.match(/(\d+)小时/)
  if (hourMatch) {
    totalMinutes += parseInt(hourMatch[1]) * 60
  }

  // 匹配分钟数
  const minuteMatch = duration.match(/(\d+)分钟/)
  if (minuteMatch) {
    totalMinutes += parseInt(minuteMatch[1])
  }

  return totalMinutes
}

// 🔥 新增：格式化分钟数为持续时间字符串
const formatMinutesToDuration = (minutes: number): string => {
  if (minutes === 0) return '0分钟'

  const days = Math.floor(minutes / (24 * 60))
  const hours = Math.floor((minutes % (24 * 60)) / 60)
  const mins = minutes % 60

  let result = ''
  if (days > 0) result += `${days}天`
  if (hours > 0) result += `${hours}小时`
  if (mins > 0) result += `${mins}分钟`

  return result || '不到1分钟'
}

// 🔥 新增：获取状态图标
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, any> = {
    delivered: CircleCheck,
    exception: Warning,
    delivery_failed: CircleClose,
    pickup_failed: CircleClose,
    submit_failed: CircleClose,
    cancelled: CircleClose,
    voided: CircleClose,
    in_transit: Loading,
    picked_up: CircleCheck,
    submitted: CircleCheck
  }

  return iconMap[status] || QuestionFilled
}

// 🔥 新增：获取状态详细信息
const getStatusDetails = (item: StatusHistoryItem): boolean => {
  return !!(item.provider || item.created_at)
}

// 🔥 新增：格式化完整日期时间
const formatFullDateTime = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// Element Plus Tag 类型定义
type TagType = 'primary' | 'success' | 'warning' | 'info' | 'danger'

// 获取时间线类型
const getTimelineType = (changeSource: string): TagType => {
  const typeMap: Record<string, TagType> = {
    callback: 'primary',
    manual: 'warning',
    system: 'info',
    api: 'success'
  }
  return typeMap[changeSource] || 'info'
}

// 获取状态标签类型
const getStatusType = (status: string): TagType => {
  const statusColors: Record<string, TagType> = {
    // 下单阶段
    submitted: 'primary',
    submit_failed: 'danger',
    
    // 揽收阶段
    assigned: 'info',
    awaiting_pickup: 'warning',
    picked_up: 'success',
    pickup_failed: 'danger',
    
    // 运输阶段
    in_transit: 'primary',
    out_for_delivery: 'warning',
    
    // 派送阶段
    delivered: 'success',
    delivery_failed: 'danger',
    delivered_abnormal: 'warning',
    
    // 计费阶段
    billed: 'info',
    
    // 异常状态
    exception: 'danger',
    returned: 'warning',
    forwarded: 'info',
    
    // 取消状态
    cancelling: 'warning',
    cancelled: 'info',
    voided: 'info',
    
    // 特殊状态
    weight_updated: 'warning',
    revived: 'success'
  }
  
  return statusColors[status] || 'info'
}

// 🔥 修复：使用统一的时间格式化工具（北京时间）
const formatTimelineDateTime = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 如果是今天，只显示时间
  if (diff < 24 * 60 * 60 * 1000) {
    const today = new Date()
    if (date.toDateString() === today.toDateString()) {
      return date.toLocaleString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }

  // 如果是昨天
  const yesterday = new Date(now)
  yesterday.setDate(yesterday.getDate() - 1)
  if (date.toDateString() === yesterday.toDateString()) {
    return '昨天 ' + date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 其他日期
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 监听订单变化
watch(() => props.order, () => {
  if (shouldLoad.value) {
    loadStatusHistory()
  }
}, { immediate: true })

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible && props.order && historyItems.value.length === 0) {
    loadStatusHistory()
  }
})

// 组件挂载时加载数据
onMounted(() => {
  if (shouldLoad.value) {
    loadStatusHistory()
  }
})
</script>

<style scoped>
.status-timeline-card {
  margin-top: 16px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 🔥 新增：状态统计样式 */
.status-statistics {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.stat-item {
  text-align: center;
  padding: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.status-change-item {
  padding: 12px 0;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.status-change-item:hover {
  background-color: #f8f9fa;
  padding-left: 8px;
  padding-right: 8px;
}

/* 🔥 新增：异常状态样式 */
.exception-status {
  background: linear-gradient(90deg, #fef0f0 0%, #fdf2f2 100%);
  border-left: 4px solid #f56c6c;
  padding-left: 12px !important;
  margin-left: -8px;
}

.exception-icon {
  margin-left: 4px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-change-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.change-source {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.status-change-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 6px;
}

.status-transition {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.duration {
  font-size: 12px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 🔥 新增：长时间持续状态警告 */
.long-duration {
  color: #e6a23c !important;
  font-weight: 500;
}

.duration-warning {
  color: #e6a23c;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.operator-info,
.change-reason {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.change-reason {
  font-style: italic;
}

/* 🔥 新增：状态详细信息样式 */
.status-details {
  margin-top: 8px;
}

.detail-content {
  font-size: 12px;
  color: #606266;
  line-height: 1.6;
}

.detail-content p {
  margin: 4px 0;
}

.detail-content strong {
  color: #303133;
  font-weight: 500;
}

/* 🔥 新增：时间线图标样式增强 */
:deep(.el-timeline-item__icon) {
  font-size: 16px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 32px;
}

:deep(.el-timeline-item__timestamp) {
  font-weight: 500;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-change-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .timeline-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .status-statistics {
    padding: 12px;
  }

  .stat-item {
    padding: 4px;
  }

  .stat-value {
    font-size: 18px;
  }

  .exception-status {
    margin-left: -4px;
    padding-left: 8px !important;
  }
}

/* 🔥 新增：深色模式支持 */
@media (prefers-color-scheme: dark) {
  .status-statistics {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: #4a5568;
  }

  .stat-value {
    color: #63b3ed;
  }

  .stat-label {
    color: #a0aec0;
  }

  .exception-status {
    background: linear-gradient(90deg, #2d1b1b 0%, #3d2020 100%);
    border-left-color: #fc8181;
  }

  .change-source {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #718096;
  }
}
</style>
