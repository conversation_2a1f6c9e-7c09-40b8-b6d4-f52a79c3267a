/**
 * 工单管理状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { WorkOrderService } from '@/api'
import type {
  WorkOrder,
  WorkOrderListRequest,
  WorkOrderListResponse,
  WorkOrderStatistics,
  WorkOrderTrend,
  WorkOrderTypeStatistics,
  WorkOrderTypeMapping
} from '@/api/model/workOrderModel'
import { ErrorHandler } from '@/utils/errorHandler'

interface WorkOrderState {
  // 工单列表
  workOrderList: WorkOrder[]
  workOrderTotal: number
  workOrderLoading: boolean

  // 工单详情
  currentWorkOrder: WorkOrder | null
  workOrderDetailLoading: boolean

  // 工单类型映射
  supportedTypes: Record<string, WorkOrderTypeMapping[]>
  typesLoading: boolean

  // 统计数据
  statistics: WorkOrderStatistics | null
  trend: WorkOrderTrend[]
  typeStatistics: WorkOrderTypeStatistics[]
  statisticsLoading: boolean

  // 缓存配置
  cacheExpiry: number
  lastFetchTime: number
}

export const useWorkOrderStore = defineStore('workOrder', () => {
  // 状态
  const state = ref<WorkOrderState>({
    workOrderList: [],
    workOrderTotal: 0,
    workOrderLoading: false,

    currentWorkOrder: null,
    workOrderDetailLoading: false,

    supportedTypes: {},
    typesLoading: false,

    statistics: null,
    trend: [],
    typeStatistics: [],
    statisticsLoading: false,

    cacheExpiry: 5 * 60 * 1000, // 5分钟缓存
    lastFetchTime: 0
  })

  // 计算属性
  const workOrderList = computed(() => state.value.workOrderList)
  const workOrderTotal = computed(() => state.value.workOrderTotal)
  const workOrderLoading = computed(() => state.value.workOrderLoading)
  const currentWorkOrder = computed(() => state.value.currentWorkOrder)
  const workOrderDetailLoading = computed(() => state.value.workOrderDetailLoading)
  const statistics = computed(() => state.value.statistics)
  const trend = computed(() => state.value.trend)
  const typeStatistics = computed(() => state.value.typeStatistics)
  const statisticsLoading = computed(() => state.value.statisticsLoading)

  // 获取支持的工单类型（带缓存）
  const getSupportedTypes = computed(() => (provider: string) => {
    return state.value.supportedTypes[provider] || []
  })

  // 检查缓存是否有效
  const isCacheValid = computed(() => {
    return Date.now() - state.value.lastFetchTime < state.value.cacheExpiry
  })

  // Actions

  /**
   * 获取工单列表
   */
  const fetchWorkOrderList = async (params?: WorkOrderListRequest, forceRefresh = false) => {
    // 如果不是强制刷新且缓存有效，直接返回
    if (!forceRefresh && isCacheValid.value && state.value.workOrderList.length > 0) {
      return {
        items: state.value.workOrderList,
        total: state.value.workOrderTotal
      }
    }

    state.value.workOrderLoading = true
    try {
      const response = await WorkOrderService.getWorkOrderList(params)

      if (response.success && response.data) {
        state.value.workOrderList = response.data.items || []
        state.value.workOrderTotal = response.data.total || 0
        state.value.lastFetchTime = Date.now()

        return response.data
      } else {
        throw new Error(response.message || '获取工单列表失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      state.value.workOrderList = []
      state.value.workOrderTotal = 0
      throw error
    } finally {
      state.value.workOrderLoading = false
    }
  }

  /**
   * 获取工单详情
   */
  const fetchWorkOrderDetail = async (id: string, forceRefresh = false) => {
    // 如果当前工单ID相同且不是强制刷新，直接返回
    if (!forceRefresh && state.value.currentWorkOrder?.id === id) {
      return state.value.currentWorkOrder
    }

    state.value.workOrderDetailLoading = true
    try {
      const workOrder = await WorkOrderService.getWorkOrderDetail(id)
      state.value.currentWorkOrder = workOrder
      return workOrder
    } catch (error) {
      ErrorHandler.handleApiError(error)
      state.value.currentWorkOrder = null
      throw error
    } finally {
      state.value.workOrderDetailLoading = false
    }
  }

  /**
   * 获取支持的工单类型
   */
  const fetchSupportedTypes = async (provider: string, forceRefresh = false) => {
    // 如果已有缓存且不是强制刷新，直接返回
    if (!forceRefresh && state.value.supportedTypes[provider]) {
      return state.value.supportedTypes[provider]
    }

    state.value.typesLoading = true
    try {
      const response = await WorkOrderService.getSupportedTypes(provider)

      if (response.success && response.data) {
        state.value.supportedTypes[provider] = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取工单类型失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      state.value.supportedTypes[provider] = []
      throw error
    } finally {
      state.value.typesLoading = false
    }
  }

  /**
   * 获取工单统计数据
   */
  const fetchStatistics = async (
    params?: {
      start_date?: string
      end_date?: string
      provider?: string
    },
    forceRefresh = false
  ) => {
    // 如果不是强制刷新且有缓存，直接返回
    if (!forceRefresh && state.value.statistics && isCacheValid.value) {
      return state.value.statistics
    }

    state.value.statisticsLoading = true
    try {
      const response = await WorkOrderService.getWorkOrderStatistics(params)

      if (response.success && response.data) {
        state.value.statistics = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取统计数据失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      state.value.statistics = null
      throw error
    } finally {
      state.value.statisticsLoading = false
    }
  }

  /**
   * 获取工单趋势数据
   */
  const fetchTrend = async (
    params?: {
      start_date?: string
      end_date?: string
      provider?: string
    },
    forceRefresh = false
  ) => {
    // 如果不是强制刷新且有缓存，直接返回
    if (!forceRefresh && state.value.trend.length > 0 && isCacheValid.value) {
      return state.value.trend
    }

    try {
      const response = await WorkOrderService.getWorkOrderTrend(params)

      if (response.success && response.data) {
        state.value.trend = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取趋势数据失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      state.value.trend = []
      throw error
    }
  }

  /**
   * 获取工单类型统计
   */
  const fetchTypeStatistics = async (
    params?: {
      start_date?: string
      end_date?: string
      provider?: string
    },
    forceRefresh = false
  ) => {
    // 如果不是强制刷新且有缓存，直接返回
    if (!forceRefresh && state.value.typeStatistics.length > 0 && isCacheValid.value) {
      return state.value.typeStatistics
    }

    try {
      const response = await WorkOrderService.getWorkOrderTypeStatistics(params)

      if (response.success && response.data) {
        state.value.typeStatistics = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取类型统计失败')
      }
    } catch (error) {
      ErrorHandler.handleApiError(error)
      state.value.typeStatistics = []
      throw error
    }
  }

  /**
   * 创建工单后更新列表
   */
  const addWorkOrder = (workOrder: WorkOrder) => {
    state.value.workOrderList.unshift(workOrder)
    state.value.workOrderTotal += 1

    // 更新统计数据
    if (state.value.statistics) {
      state.value.statistics.total += 1
      state.value.statistics.pending += 1
      state.value.statistics.today_created += 1
    }
  }

  /**
   * 更新工单状态
   */
  const updateWorkOrderStatus = (id: string, status: number) => {
    // 更新列表中的工单
    const index = state.value.workOrderList.findIndex((item) => item.id === id)
    if (index > -1) {
      const oldStatus = state.value.workOrderList[index].status
      state.value.workOrderList[index].status = status
      state.value.workOrderList[index].updated_at = new Date().toISOString()

      // 如果是当前查看的工单，也要更新
      if (state.value.currentWorkOrder?.id === id) {
        state.value.currentWorkOrder.status = status
        state.value.currentWorkOrder.updated_at = new Date().toISOString()
      }

      // 更新统计数据
      if (state.value.statistics) {
        // 减少旧状态计数
        switch (oldStatus) {
          case 1:
            state.value.statistics.pending -= 1
            break
          case 2:
            state.value.statistics.processing -= 1
            break
          case 3:
            state.value.statistics.completed -= 1
            break
        }

        // 增加新状态计数
        switch (status) {
          case 1:
            state.value.statistics.pending += 1
            break
          case 2:
            state.value.statistics.processing += 1
            break
          case 3:
            state.value.statistics.completed += 1
            break
        }
      }
    }
  }

  /**
   * 清除缓存
   */
  const clearCache = () => {
    state.value.workOrderList = []
    state.value.workOrderTotal = 0
    state.value.currentWorkOrder = null
    state.value.statistics = null
    state.value.trend = []
    state.value.typeStatistics = []
    state.value.lastFetchTime = 0
  }

  /**
   * 重置状态
   */
  const resetState = () => {
    Object.assign(state.value, {
      workOrderList: [],
      workOrderTotal: 0,
      workOrderLoading: false,
      currentWorkOrder: null,
      workOrderDetailLoading: false,
      supportedTypes: {},
      typesLoading: false,
      statistics: null,
      trend: [],
      typeStatistics: [],
      statisticsLoading: false,
      lastFetchTime: 0
    })
  }

  return {
    // 状态
    workOrderList,
    workOrderTotal,
    workOrderLoading,
    currentWorkOrder,
    workOrderDetailLoading,
    statistics,
    trend,
    typeStatistics,
    statisticsLoading,
    getSupportedTypes,
    isCacheValid,

    // Actions
    fetchWorkOrderList,
    fetchWorkOrderDetail,
    fetchSupportedTypes,
    fetchStatistics,
    fetchTrend,
    fetchTypeStatistics,
    addWorkOrder,
    updateWorkOrderStatus,
    clearCache,
    resetState
  }
})
