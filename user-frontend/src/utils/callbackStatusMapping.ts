// 回调状态细分映射工具
// 🔥 新增：将宽泛的事件类型细分为具体的业务状态

export interface CallbackStatusDetail {
  category: string // 状态分类
  name: string // 状态名称
  description: string // 状态描述
  color: string // 显示颜色
  icon: string // 图标
}

// 订单状态细分映射
export const ORDER_STATUS_MAPPING: Record<string, CallbackStatusDetail> = {
  // 下单相关
  created: {
    category: '下单',
    name: '已下单',
    description: '订单已创建',
    color: '#409EFF',
    icon: 'Document'
  },
  confirmed: {
    category: '下单',
    name: '已确认',
    description: '订单已确认',
    color: '#409EFF',
    icon: 'Select'
  },

  // 揽收相关
  assigned: {
    category: '揽收',
    name: '已分配快递员',
    description: '已分配快递员，等待揽收',
    color: '#E6A23C',
    icon: 'User'
  },
  awaiting_pickup: {
    category: '揽收',
    name: '等待揽收',
    description: '等待快递员上门揽收',
    color: '#E6A23C',
    icon: 'Clock'
  },
  picked_up: {
    category: '揽收',
    name: '已揽收',
    description: '快递员已上门取件',
    color: '#67C23A',
    icon: 'Check'
  },
  collected: {
    category: '揽收',
    name: '已收件',
    description: '快递已收件',
    color: '#67C23A',
    icon: 'Box'
  },
  collecting: {
    category: '揽收',
    name: '收件中',
    description: '快递员正在收件',
    color: '#E6A23C',
    icon: 'Loading'
  },

  // 运输相关
  in_transit: {
    category: '运输',
    name: '运输中',
    description: '包裹正在运输途中',
    color: '#409EFF',
    icon: 'Van'
  },
  arrived_at_destination: {
    category: '运输',
    name: '到达目的地',
    description: '包裹已到达目的地城市',
    color: '#409EFF',
    icon: 'LocationInformation'
  },
  out_for_delivery: {
    category: '派送',
    name: '派送中',
    description: '包裹正在派送中',
    color: '#E6A23C',
    icon: 'Van'
  },

  // 签收相关
  delivered: {
    category: '签收',
    name: '已签收',
    description: '包裹已成功签收',
    color: '#67C23A',
    icon: 'SuccessFilled'
  },
  self_pickup: {
    category: '签收',
    name: '自提',
    description: '包裹已放置自提点',
    color: '#67C23A',
    icon: 'Shop'
  },

  // 异常相关
  exception: {
    category: '异常',
    name: '异常',
    description: '包裹运输异常',
    color: '#F56C6C',
    icon: 'WarningFilled'
  },
  rejected: {
    category: '异常',
    name: '拒收',
    description: '收件人拒收包裹',
    color: '#F56C6C',
    icon: 'CircleCloseFilled'
  },
  returned: {
    category: '异常',
    name: '退回',
    description: '包裹已退回发件人',
    color: '#F56C6C',
    icon: 'RefreshLeft'
  },
  lost: {
    category: '异常',
    name: '丢失',
    description: '包裹丢失',
    color: '#F56C6C',
    icon: 'QuestionFilled'
  },
  damaged: {
    category: '异常',
    name: '破损',
    description: '包裹破损',
    color: '#F56C6C',
    icon: 'WarningFilled'
  },
  exception_delivered: {
    category: '异常',
    name: '异常签收',
    description: '包裹异常签收',
    color: '#E6A23C',
    icon: 'WarningFilled'
  },
  pickup_failed: {
    category: '异常',
    name: '揽收失败',
    description: '快递员揽收失败',
    color: '#F56C6C',
    icon: 'CircleCloseFilled'
  },
  submit_failed: {
    category: '异常',
    name: '提交失败',
    description: '订单提交失败',
    color: '#F56C6C',
    icon: 'CircleCloseFilled'
  },

  // 取消相关
  cancelled: {
    category: '取消',
    name: '已取消',
    description: '订单已取消',
    color: '#909399',
    icon: 'CircleClose'
  },
  timeout_cancelled: {
    category: '取消',
    name: '超时取消',
    description: '订单超时自动取消',
    color: '#909399',
    icon: 'AlarmClock'
  },

  // 🔥 新增：其他状态
  weight_updated: {
    category: '计费',
    name: '重量更新',
    description: '包裹重量信息更新',
    color: '#E6A23C',
    icon: 'Scale'
  },
  billed: {
    category: '计费',
    name: '已计费',
    description: '订单已完成计费',
    color: '#67C23A',
    icon: 'Money'
  },
  forwarded: {
    category: '运输',
    name: '已转寄',
    description: '包裹已转寄',
    color: '#409EFF',
    icon: 'Right'
  },
  ticket_replied: {
    category: '工单',
    name: '工单回复',
    description: '客服工单回复',
    color: '#909399',
    icon: 'ChatDotRound'
  },
  unknown: {
    category: '其他',
    name: '未知状态',
    description: '未知的回调状态',
    color: '#C0C4CC',
    icon: 'QuestionFilled'
  }
}

// 计费类型细分映射
export const BILLING_TYPE_MAPPING: Record<string, CallbackStatusDetail> = {
  freight: {
    category: '计费',
    name: '运费',
    description: '基础运输费用',
    color: '#409EFF',
    icon: 'Money'
  },
  insurance: {
    category: '计费',
    name: '保价费',
    description: '保价服务费用',
    color: '#E6A23C',
    icon: 'Lock'
  },
  packaging: {
    category: '计费',
    name: '包装费',
    description: '包装材料费用',
    color: '#909399',
    icon: 'Box'
  },
  fuel: {
    category: '计费',
    name: '燃油费',
    description: '燃油附加费',
    color: '#F56C6C',
    icon: 'Lightning'
  },
  return: {
    category: '计费',
    name: '逆向费',
    description: '退货运输费用',
    color: '#909399',
    icon: 'RefreshLeft'
  },
  other: {
    category: '计费',
    name: '其他费用',
    description: '其他杂项费用',
    color: '#909399',
    icon: 'More'
  }
}

// 工单类型细分映射
export const TICKET_TYPE_MAPPING: Record<string, CallbackStatusDetail> = {
  pickup_urge: {
    category: '工单',
    name: '催揽收',
    description: '催促快递员揽收',
    color: '#E6A23C',
    icon: 'Bell'
  },
  weight_exception: {
    category: '工单',
    name: '重量异常',
    description: '包裹重量异常处理',
    color: '#F56C6C',
    icon: 'Warning'
  },
  fake_pickup: {
    category: '工单',
    name: '虚假揽收',
    description: '虚假揽收投诉',
    color: '#F56C6C',
    icon: 'WarningFilled'
  },
  damage: {
    category: '工单',
    name: '破损',
    description: '包裹破损处理',
    color: '#F56C6C',
    icon: 'WarningFilled'
  },
  loss: {
    category: '工单',
    name: '遗失',
    description: '包裹遗失处理',
    color: '#F56C6C',
    icon: 'QuestionFilled'
  },
  delivery_urge: {
    category: '工单',
    name: '催派送',
    description: '催促快递员派送',
    color: '#E6A23C',
    icon: 'Timer'
  },
  cancel_order: {
    category: '工单',
    name: '取消运单',
    description: '取消运单申请',
    color: '#909399',
    icon: 'CircleClose'
  }
}

// 🔥 修复：获取状态详情（支持状态分类参数，与后端保持一致）
export function getStatusDetail(
  eventType: string,
  statusCode?: string,
  extraData?: any,
  statusCategory?: string
): CallbackStatusDetail {
  // 🔥 优先使用传入的状态分类
  if (statusCategory) {
    // 规则：计费/工单固定展示分类默认名称，避免被状态码误映射
    if (statusCategory !== '计费' && statusCategory !== '工单') {
      if (statusCode && ORDER_STATUS_MAPPING[statusCode]) {
        const detail = ORDER_STATUS_MAPPING[statusCode]
        return {
          ...detail,
          category: statusCategory
        }
      }
    }

    // 根据状态分类返回默认状态
    switch (statusCategory) {
      case '状态更新':
        return {
          category: '状态更新',
          name: getStatusName(statusCode) || '状态更新',
          description: '订单状态发生变更',
          color: '#409EFF',
          icon: 'Refresh'
        }
      case '计费':
        return {
          category: '计费',
          name: '计费更新',
          description: '订单计费信息更新',
          color: '#E6A23C',
          icon: 'Money'
        }
      case '揽收':
        return {
          category: '揽收',
          name: '揽收通知',
          description: '快递员揽收通知',
          color: '#67C23A',
          icon: 'Check'
        }
      case '工单':
        return {
          category: '工单',
          name: '工单回复',
          description: '工单处理结果通知',
          color: '#909399',
          icon: 'ChatDotRound'
        }
      default:
        return {
          category: statusCategory,
          name: statusCategory,
          description: `${statusCategory}相关通知`,
          color: '#909399',
          icon: 'Bell'
        }
    }
  }

  // 🔥 新增：检查是否为揽件员推送（event_type: 3 或包含courier信息）
  if (eventType === '3' ||
      (extraData?.event_type === 3) ||
      (extraData?.courier?.phone) ||
      (extraData?.data?.courier?.phone)) {
    return {
      category: '揽收',
      name: '已分配揽件员',
      description: '快递员已分配，准备揽收',
      color: '#67C23A',
      icon: 'User'
    }
  }

  // 🔥 兼容原有逻辑：根据事件类型和状态码获取详细信息
  switch (eventType) {
    case 'order_status_changed':
      if (statusCode && ORDER_STATUS_MAPPING[statusCode]) {
        return ORDER_STATUS_MAPPING[statusCode]
      }
      // 尝试从额外数据中提取状态
      if (extraData?.status) {
        const status = extraData.status.toLowerCase()
        if (ORDER_STATUS_MAPPING[status]) {
          return ORDER_STATUS_MAPPING[status]
        }
      }
      // 默认状态更新
      return {
        category: '状态更新',
        name: '状态更新',
        description: '订单状态发生变更',
        color: '#409EFF',
        icon: 'Refresh'
      }

    case 'billing_updated':
      if (extraData?.fee_type && BILLING_TYPE_MAPPING[extraData.fee_type]) {
        return BILLING_TYPE_MAPPING[extraData.fee_type]
      }
      return {
        category: '计费',
        name: '计费更新',
        description: '订单计费信息更新',
        color: '#E6A23C',
        icon: 'Money'
      }

    case 'ticket_replied':
    case 'workorder.replied':
      if (extraData?.ticket_type && TICKET_TYPE_MAPPING[extraData.ticket_type]) {
        return TICKET_TYPE_MAPPING[extraData.ticket_type]
      }
      return {
        category: '工单',
        name: '工单回复',
        description: '工单处理结果通知',
        color: '#909399',
        icon: 'ChatDotRound'
      }

    default:
      return {
        category: '其他',
        name: '未知事件',
        description: '未知的回调事件',
        color: '#909399',
        icon: 'QuestionFilled'
      }
  }
}

// 🔥 新增：根据状态码获取状态名称
function getStatusName(statusCode?: string): string {
  if (!statusCode) return '未知状态'

  const statusNames: Record<string, string> = {
    created: '已创建',
    in_transit: '运输中',
    delivered: '已签收',
    exception: '异常',
    cancelled: '已取消',
    picked_up: '已揽收',
    out_for_delivery: '派送中'
  }

  return statusNames[statusCode] || statusCode
}

// 获取状态分类列表（用于筛选）
export function getStatusCategories(): Array<{ label: string; value: string }> {
  const categories = new Set<string>()

  Object.values(ORDER_STATUS_MAPPING).forEach((item) => categories.add(item.category))
  Object.values(BILLING_TYPE_MAPPING).forEach((item) => categories.add(item.category))
  Object.values(TICKET_TYPE_MAPPING).forEach((item) => categories.add(item.category))

  return Array.from(categories).map((category) => ({
    label: category,
    value: category
  }))
}

// 获取具体状态列表（用于筛选）
export function getStatusOptions(): Array<{ label: string; value: string; category: string }> {
  const options: Array<{ label: string; value: string; category: string }> = []

  Object.entries(ORDER_STATUS_MAPPING).forEach(([key, value]) => {
    options.push({
      label: value.name,
      value: key,
      category: value.category
    })
  })

  Object.entries(BILLING_TYPE_MAPPING).forEach(([key, value]) => {
    options.push({
      label: value.name,
      value: key,
      category: value.category
    })
  })

  Object.entries(TICKET_TYPE_MAPPING).forEach(([key, value]) => {
    options.push({
      label: value.name,
      value: key,
      category: value.category
    })
  })

  return options.sort((a, b) => a.label.localeCompare(b.label))
}
