package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"callback-receiver/internal/config"
	"callback-receiver/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	_ "github.com/lib/pq"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// CallbackNotification 回调通知消息
type CallbackNotification struct {
	ID       string `json:"id"`
	Provider string `json:"provider"`
}

// CallbackRawData 原始回调数据
type CallbackRawData struct {
	ID         string            `json:"id"`
	Provider   string            `json:"provider"`
	RawBody    string            `json:"raw_body"`
	Headers    map[string]string `json:"headers"`
	ClientIP   string            `json:"client_ip"`
	ReceivedAt time.Time         `json:"received_at"`
	Processed  bool              `json:"processed"`
	CreatedAt  time.Time         `json:"created_at"`
}

// SimpleCallbackConsumer 简单的回调消费者
type SimpleCallbackConsumer struct {
	receiverDB    *sql.DB
	mainSystemURL string
	redis         *redis.Client
	queueName     string
	httpClient    *http.Client
}

func NewSimpleCallbackConsumer(receiverDB *sql.DB, mainSystemURL string, redis *redis.Client, queueName string) *SimpleCallbackConsumer {
	return &SimpleCallbackConsumer{
		receiverDB:    receiverDB,
		mainSystemURL: mainSystemURL,
		redis:         redis,
		queueName:     queueName,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Start 启动消费者
func (c *SimpleCallbackConsumer) Start(ctx context.Context) error {
	log.Printf("启动回调消费者，队列: %s", c.queueName)

	for {
		select {
		case <-ctx.Done():
			log.Println("停止回调消费者")
			return ctx.Err()
		default:
			// 从Redis队列获取通知 (同时监听主队列和延迟队列)
			delayedQueueName := c.queueName + "_delayed"
			result, err := c.redis.BRPop(ctx, 5*time.Second, c.queueName, delayedQueueName).Result()
			if err != nil {
				if err == redis.Nil {
					continue
				}
				log.Printf("从Redis获取消息失败: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}

			if len(result) < 2 {
				continue
			}

			// 解析通知消息
			var notification CallbackNotification
			if err := json.Unmarshal([]byte(result[1]), &notification); err != nil {
				log.Printf("解析通知消息失败: %v", err)
				continue
			}

			// 处理回调
			if err := c.processCallback(ctx, &notification); err != nil {
				log.Printf("处理回调失败: ID=%s, Provider=%s, Error=%v",
					notification.ID, notification.Provider, err)

				// 🔥 关键修复：失败的消息重新入队，延迟处理
				if err := c.requeueFailedMessage(&notification); err != nil {
					log.Printf("重新入队失败: ID=%s, Error=%v", notification.ID, err)
				} else {
					log.Printf("失败消息已重新入队: ID=%s", notification.ID)
				}
			}
		}
	}
}

// processCallback 处理回调
func (c *SimpleCallbackConsumer) processCallback(ctx context.Context, notification *CallbackNotification) error {
	log.Printf("🔄 开始处理回调: ID=%s, Provider=%s", notification.ID, notification.Provider)

	// 1. 从回调接收服务数据库获取原始数据
	rawData, err := c.getRawCallbackData(ctx, notification.ID)
	if err != nil {
		log.Printf("❌ 获取原始回调数据失败: ID=%s, Error=%v", notification.ID, err)
		return fmt.Errorf("获取原始回调数据失败: %w", err)
	}

	// 🔥 优化：记录关键业务信息
	orderInfo := c.extractOrderInfo(rawData)
	log.Printf("📋 回调详情: ID=%s, Provider=%s, ClientIP=%s, OrderInfo=%s",
		notification.ID, notification.Provider, rawData.ClientIP, orderInfo)

	// 2. 转发到主系统
	log.Printf("📤 转发到主系统: ID=%s, URL=%s/api/v1/callbacks/%s",
		notification.ID, c.mainSystemURL, rawData.Provider)

	err = c.forwardToMainSystem(ctx, rawData)
	if err != nil {
		log.Printf("❌ 转发到主系统失败: ID=%s, Error=%v", notification.ID, err)
		return fmt.Errorf("转发到主系统失败: %w", err)
	}

	// 3. 标记为已处理
	err = c.markAsProcessed(ctx, notification.ID)
	if err != nil {
		log.Printf("⚠️ 标记已处理失败: ID=%s, Error=%v", notification.ID, err)
		// 不返回错误，因为业务逻辑已经处理成功
	}

	log.Printf("✅ 回调处理完成: ID=%s, OrderInfo=%s", notification.ID, orderInfo)
	return nil
}

// forwardToMainSystem 转发到主系统 (带重试机制)
func (c *SimpleCallbackConsumer) forwardToMainSystem(ctx context.Context, data *CallbackRawData) error {
	const maxRetries = 3
	const baseDelay = 1 * time.Second

	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			// 指数退避: 1s, 2s, 4s
			delay := baseDelay * time.Duration(1<<uint(attempt-1))
			log.Printf("重试转发到主系统 (第%d次), 等待%v: ID=%s", attempt+1, delay, data.ID)
			time.Sleep(delay)
		}

		err := c.doForwardRequest(ctx, data)
		if err == nil {
			if attempt > 0 {
				log.Printf("重试成功: ID=%s, 尝试次数=%d", data.ID, attempt+1)
			}
			return nil
		}

		lastErr = err
		log.Printf("转发失败 (第%d次): ID=%s, Error=%v", attempt+1, data.ID, err)

		// 检查是否是不可重试的错误
		if !c.isRetryableError(err) {
			log.Printf("不可重试的错误，停止重试: ID=%s, Error=%v", data.ID, err)
			break
		}
	}

	return fmt.Errorf("转发失败，已重试%d次: %w", maxRetries, lastErr)
}

// doForwardRequest 执行实际的转发请求
func (c *SimpleCallbackConsumer) doForwardRequest(ctx context.Context, data *CallbackRawData) error {
	// 🔥 修复：根据回调类型选择不同的端点
	var url string
	if isWorkOrderCallback(data) {
		endpoint := c.getWorkOrderCallbackEndpoint(data.Provider)
		url = fmt.Sprintf("%s%s", c.mainSystemURL, endpoint)
		log.Printf("🔧 工单回调转发: %s", url)
	} else {
		url = fmt.Sprintf("%s/api/v1/callbacks/%s", c.mainSystemURL, data.Provider)
		log.Printf("📦 物流回调转发: %s", url)
	}

	// 使用原始body作为请求体
	req, err := http.NewRequestWithContext(ctx, "POST", url,
		strings.NewReader(data.RawBody))
	if err != nil {
		return err
	}

	// 设置请求头 (过滤掉可能有问题的头)
	originalContentType := ""
	for key, value := range data.Headers {
		// 跳过可能有问题的头
		if key == "Content-Length" || key == "Transfer-Encoding" {
			continue
		}
		if key == "Content-Type" {
			originalContentType = value
		}
		req.Header.Set(key, value)
	}

	// 🔥 修复：保持原始Content-Type，不要强制设置为JSON
	if originalContentType == "" {
		req.Header.Set("Content-Type", "application/json")
	}
	req.Header.Set("X-Forwarded-For", data.ClientIP)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("主系统返回错误状态码: %d", resp.StatusCode)
	}

	return nil
}

// isRetryableError 判断错误是否可重试
func (c *SimpleCallbackConsumer) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()

	// 网络连接错误 - 可重试
	if strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "connection reset") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "no such host") {
		return true
	}

	// 5xx服务器错误 - 可重试
	if strings.Contains(errStr, "错误状态码: 5") {
		return true
	}

	// 4xx客户端错误 - 不可重试
	if strings.Contains(errStr, "错误状态码: 4") {
		return false
	}

	// 其他错误默认可重试
	return true
}

// getRawCallbackData 获取原始回调数据
func (c *SimpleCallbackConsumer) getRawCallbackData(ctx context.Context, id string) (*CallbackRawData, error) {
	query := `
		SELECT id, provider, raw_body, headers, client_ip,
		       received_at, processed, created_at
		FROM callback_raw_data
		WHERE id = $1
	`

	row := c.receiverDB.QueryRowContext(ctx, query, id)

	var data CallbackRawData
	var headersJSON []byte

	err := row.Scan(
		&data.ID,
		&data.Provider,
		&data.RawBody,
		&headersJSON,
		&data.ClientIP,
		&data.ReceivedAt,
		&data.Processed,
		&data.CreatedAt,
	)

	if err != nil {
		return nil, err
	}

	// 反序列化headers
	err = json.Unmarshal(headersJSON, &data.Headers)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

// markAsProcessed 标记为已处理
func (c *SimpleCallbackConsumer) markAsProcessed(ctx context.Context, id string) error {
	query := `UPDATE callback_raw_data SET processed = true WHERE id = $1`
	_, err := c.receiverDB.ExecContext(ctx, query, id)
	return err
}

// requeueFailedMessage 重新入队失败的消息
func (c *SimpleCallbackConsumer) requeueFailedMessage(notification *CallbackNotification) error {
	// 🔥 更可靠的方案：重新发送到Redis队列
	messageData, err := json.Marshal(notification)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	// 重新入队到主队列 (会被重新处理)
	err = c.redis.LPush(context.Background(), c.queueName, messageData).Err()
	if err != nil {
		return fmt.Errorf("重新入队失败: %w", err)
	}

	return nil
}

// startFailedMessageRecovery 启动失败消息恢复机制
func (c *SimpleCallbackConsumer) startFailedMessageRecovery(ctx context.Context) {
	ticker := time.NewTicker(60 * time.Second) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			if err := c.recoverFailedMessages(ctx); err != nil {
				log.Printf("恢复失败消息时出错: %v", err)
			}
		}
	}
}

// recoverFailedMessages 恢复失败的消息
func (c *SimpleCallbackConsumer) recoverFailedMessages(ctx context.Context) error {
	// 查找超过5分钟未处理的消息
	query := `
		SELECT id, provider
		FROM callback_raw_data
		WHERE processed = false
		AND received_at < NOW() - INTERVAL '5 minutes'
		LIMIT 10
	`

	rows, err := c.receiverDB.QueryContext(ctx, query)
	if err != nil {
		return err
	}
	defer rows.Close()

	var recovered int
	for rows.Next() {
		var id, provider string
		if err := rows.Scan(&id, &provider); err != nil {
			continue
		}

		// 重新入队
		notification := &CallbackNotification{
			ID:       id,
			Provider: provider,
		}

		if err := c.requeueFailedMessage(notification); err != nil {
			log.Printf("恢复消息失败: ID=%s, Error=%v", id, err)
		} else {
			recovered++
			log.Printf("恢复消息: ID=%s", id)
		}
	}

	if recovered > 0 {
		log.Printf("本次恢复了 %d 条失败消息", recovered)
	}

	return nil
}

// extractOrderInfo 从回调数据中提取关键订单信息用于日志记录
func (c *SimpleCallbackConsumer) extractOrderInfo(data *CallbackRawData) string {
	if data == nil || data.RawBody == "" {
		return "无数据"
	}

	// 尝试解析JSON获取订单信息
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(data.RawBody), &jsonData); err != nil {
		return fmt.Sprintf("解析失败: %s", data.RawBody[:min(100, len(data.RawBody))])
	}

	// 根据不同供应商提取关键信息
	switch data.Provider {
	case "kuaidi100":
		return c.extractKuaidi100Info(jsonData)
	case "yida":
		return c.extractYidaInfo(jsonData)
	case "yuntong":
		return c.extractYuntongInfo(jsonData)
	default:
		return fmt.Sprintf("未知供应商: %s", data.RawBody[:min(200, len(data.RawBody))])
	}
}

// extractKuaidi100Info 提取快递100回调信息
func (c *SimpleCallbackConsumer) extractKuaidi100Info(data map[string]interface{}) string {
	var info []string

	if param, ok := data["param"].(string); ok {
		var paramData map[string]interface{}
		if err := json.Unmarshal([]byte(param), &paramData); err == nil {
			if orderNo, ok := paramData["orderId"].(string); ok {
				info = append(info, fmt.Sprintf("订单号:%s", orderNo))
			}
			if trackingNo, ok := paramData["kuaidinum"].(string); ok {
				info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
			}
			if status, ok := paramData["status"].(float64); ok {
				statusText := c.getKuaidi100StatusText(int(status))
				info = append(info, fmt.Sprintf("状态:%s(%d)", statusText, int(status)))
			}
		}
	}

	if len(info) == 0 {
		return "快递100回调(无订单信息)"
	}
	return fmt.Sprintf("快递100: %s", strings.Join(info, ", "))
}

// extractYidaInfo 提取易达回调信息
func (c *SimpleCallbackConsumer) extractYidaInfo(data map[string]interface{}) string {
	var info []string

	if orderNo, ok := data["orderNo"].(string); ok {
		info = append(info, fmt.Sprintf("订单号:%s", orderNo))
	}
	if trackingNo, ok := data["deliveryId"].(string); ok {
		info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
	}
	if status, ok := data["status"].(float64); ok {
		statusText := c.getYidaStatusText(int(status))
		info = append(info, fmt.Sprintf("状态:%s(%d)", statusText, int(status)))
	}

	if len(info) == 0 {
		return "易达回调(无订单信息)"
	}
	return fmt.Sprintf("易达: %s", strings.Join(info, ", "))
}

// extractYuntongInfo 提取云通回调信息
func (c *SimpleCallbackConsumer) extractYuntongInfo(data map[string]interface{}) string {
	var info []string

	if orderNo, ok := data["OrderCode"].(string); ok {
		info = append(info, fmt.Sprintf("订单号:%s", orderNo))
	}
	if trackingNo, ok := data["LogisticCode"].(string); ok {
		info = append(info, fmt.Sprintf("运单号:%s", trackingNo))
	}
	if state, ok := data["State"].(float64); ok {
		statusText := c.getYuntongStatusText(int(state))
		info = append(info, fmt.Sprintf("状态:%s(%d)", statusText, int(state)))
	}

	if len(info) == 0 {
		return "云通回调(无订单信息)"
	}
	return fmt.Sprintf("云通: %s", strings.Join(info, ", "))
}

// getKuaidi100StatusText 获取快递100状态文本
func (c *SimpleCallbackConsumer) getKuaidi100StatusText(status int) string {
	statusMap := map[int]string{
		0: "暂无轨迹", 1: "已揽收", 2: "在途中", 3: "已签收", 4: "问题件", 5: "疑难件", 6: "退件签收",
	}
	if text, ok := statusMap[status]; ok {
		return text
	}
	return "未知状态"
}

// getYidaStatusText 获取易达状态文本
func (c *SimpleCallbackConsumer) getYidaStatusText(status int) string {
	statusMap := map[int]string{
		1: "已分配", 2: "已揽收", 3: "运输中", 4: "派送中", 5: "已签收", 6: "异常", 7: "拒收",
	}
	if text, ok := statusMap[status]; ok {
		return text
	}
	return "未知状态"
}

// getYuntongStatusText 获取云通状态文本
func (c *SimpleCallbackConsumer) getYuntongStatusText(state int) string {
	statusMap := map[int]string{
		0: "暂无轨迹", 1: "已揽收", 2: "在途中", 3: "已签收", 4: "问题件",
	}
	if text, ok := statusMap[state]; ok {
		return text
	}
	return "未知状态"
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func main() {
	// 🕐 设置应用程序时区为北京时间
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		log.Fatalf("设置时区失败: %v", err)
	}
	time.Local = location
	log.Printf("✅ 应用程序时区已设置为: %s", location.String())

	fmt.Println("🚀 启动回调消费者服务...")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}
	fmt.Printf("✅ 配置加载成功 - 主系统: %s\n", cfg.MainSystem.URL)

	// 初始化统计信息
	statsLock.Lock()
	consumerStats.WorkerCount = cfg.Consumer.WorkerCount
	consumerStats.QueueName = cfg.Redis.QueueName
	statsLock.Unlock()

	// 初始化Redis客户端
	opt, err := redis.ParseURL(cfg.Redis.URL)
	if err != nil {
		log.Fatalf("解析Redis URL失败: %v", err)
	}

	// 应用Redis配置
	opt.PoolSize = cfg.Redis.PoolSize
	opt.MinIdleConns = cfg.Redis.MinIdleConns
	opt.MaxConnAge = time.Duration(cfg.Redis.MaxConnAge) * time.Second
	opt.PoolTimeout = time.Duration(cfg.Redis.PoolTimeout) * time.Second
	opt.IdleTimeout = time.Duration(cfg.Redis.IdleTimeout) * time.Second
	opt.DialTimeout = cfg.Redis.DialTimeout
	opt.ReadTimeout = cfg.Redis.ReadTimeout
	opt.WriteTimeout = cfg.Redis.WriteTimeout

	rdb := redis.NewClient(opt)
	defer rdb.Close()

	// 测试Redis连接
	ctx := context.Background()
	if err := rdb.Ping(ctx).Err(); err != nil {
		log.Fatalf("Redis连接失败: %v", err)
	}
	fmt.Printf("✅ Redis连接成功: %s\n", cfg.Redis.URL)

	// 初始化Logger
	logger, err := initLogger(cfg)
	if err != nil {
		log.Fatalf("初始化Logger失败: %v", err)
	}
	defer logger.Sync()

	// 初始化工单回调服务
	workorderService := service.NewWorkOrderCallbackService(logger)

	// 创建HTTP管理服务器
	var managementServer *http.Server
	if cfg.Consumer.EnableManagementAPI && cfg.Consumer.Port != "" {
		gin.SetMode(gin.ReleaseMode)
		r := gin.New()
		r.Use(gin.Recovery())

		// 健康检查端点
		r.GET(cfg.Consumer.ManagementEndpoints.Health, func(c *gin.Context) {
			c.JSON(200, gin.H{
				"status":    "ok",
				"service":   "callback-consumer",
				"version":   cfg.Service.Version,
				"timestamp": time.Now().Unix(),
			})
		})

		// 状态端点
		r.GET(cfg.Consumer.ManagementEndpoints.Status, func(c *gin.Context) {
			statsLock.RLock()
			stats := *consumerStats
			statsLock.RUnlock()

			// 获取当前队列长度
			queueLen, _ := rdb.LLen(ctx, cfg.Redis.QueueName).Result()
			stats.QueueLength = queueLen

			c.JSON(200, stats)
		})

		// 队列状态端点
		r.GET(cfg.Consumer.ManagementEndpoints.Queue, func(c *gin.Context) {
			queueLen, _ := rdb.LLen(ctx, cfg.Redis.QueueName).Result()
			delayedLen, _ := rdb.LLen(ctx, cfg.Redis.DelayedQueueName).Result()
			dlqLen, _ := rdb.LLen(ctx, cfg.Recovery.DeadLetterQueue.QueueName).Result()

			c.JSON(200, gin.H{
				"main_queue": gin.H{
					"name":   cfg.Redis.QueueName,
					"length": queueLen,
				},
				"delayed_queue": gin.H{
					"name":   cfg.Redis.DelayedQueueName,
					"length": delayedLen,
				},
				"dead_letter_queue": gin.H{
					"name":   cfg.Recovery.DeadLetterQueue.QueueName,
					"length": dlqLen,
				},
			})
		})

		// 指标端点
		r.GET(cfg.Consumer.ManagementEndpoints.Metrics, func(c *gin.Context) {
			statsLock.RLock()
			stats := *consumerStats
			statsLock.RUnlock()

			uptime := time.Since(stats.StartTime)
			c.JSON(200, gin.H{
				"uptime_seconds":  uptime.Seconds(),
				"processed_total": stats.ProcessedCount,
				"failed_total":    stats.FailedCount,
				"success_rate":    getSuccessRate(stats.ProcessedCount, stats.FailedCount),
				"worker_count":    stats.WorkerCount,
				"queue_length":    stats.QueueLength,
			})
		})

		managementServer = &http.Server{
			Addr:    ":" + cfg.Consumer.Port,
			Handler: r,
		}

		// 启动管理服务器
		go func() {
			fmt.Printf("✅ 消费者管理API已启动: http://localhost:%s\n", cfg.Consumer.Port)
			if err := managementServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.Printf("管理服务器启动失败: %v", err)
			}
		}()
	}

	// 创建工作池
	var wg sync.WaitGroup
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// 启动消费者工作器
	for i := 0; i < cfg.Consumer.WorkerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			runWorker(ctx, workerID, rdb, cfg, workorderService, quit)
		}(i + 1)
	}

	fmt.Printf("✅ 启动了 %d 个消费者工作器\n", cfg.Consumer.WorkerCount)
	fmt.Printf("📊 监听队列: %s\n", cfg.Redis.QueueName)
	fmt.Printf("🎯 目标系统: %s\n", cfg.MainSystem.URL)

	// 等待信号
	<-quit
	fmt.Println("\n🛑 收到停止信号，正在优雅关闭...")

	// 关闭管理服务器
	if managementServer != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := managementServer.Shutdown(shutdownCtx); err != nil {
			log.Printf("管理服务器关闭失败: %v", err)
		}
	}

	// 等待所有工作器完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 超时控制
	timeout := time.After(30 * time.Second)
	select {
	case <-done:
		fmt.Println("✅ 所有工作器已安全停止")
	case <-timeout:
		fmt.Println("⚠️ 超时，强制退出")
	}
}

func runWorker(ctx context.Context, workerID int, rdb *redis.Client, cfg *config.Config, workorderService *service.WorkOrderCallbackService, quit chan os.Signal) {
	ticker := time.NewTicker(cfg.Consumer.PollInterval)
	defer ticker.Stop()

	fmt.Printf("🔄 工作器 %d 已启动\n", workerID)

	for {
		select {
		case <-quit:
			fmt.Printf("🛑 工作器 %d 接收到停止信号\n", workerID)
			return
		case <-ticker.C:
			// 从队列获取消息 (批量处理)
			messages, err := rdb.LPopCount(ctx, cfg.Redis.QueueName, cfg.Consumer.BatchSize).Result()
			if err != nil {
				if err != redis.Nil {
					fmt.Printf("❌ 工作器 %d 获取消息失败: %v\n", workerID, err)
				}
				continue
			}

			if len(messages) == 0 {
				continue
			}

			fmt.Printf("📦 工作器 %d 获取到 %d 条消息\n", workerID, len(messages))

			// 处理消息
			for _, message := range messages {
				// 添加更明显的开始日志
				log.Printf("🔄 [CONSUMER] 工作器 %d 开始处理消息: %s", workerID, message[:min(100, len(message))])

				if err := processMessage(ctx, workerID, message, cfg, workorderService); err != nil {
					fmt.Printf("❌ 工作器 %d 处理消息失败: %v\n", workerID, err)
					log.Printf("❌ [CONSUMER] 工作器 %d 处理消息失败: %v", workerID, err)

					// 更新失败统计
					statsLock.Lock()
					consumerStats.FailedCount++
					statsLock.Unlock()

					// 失败重试逻辑
					if err := handleFailedMessage(ctx, rdb, cfg, message, err); err != nil {
						fmt.Printf("❌ 工作器 %d 处理失败消息错误: %v\n", workerID, err)
						log.Printf("❌ [CONSUMER] 工作器 %d 处理失败消息错误: %v", workerID, err)
					}
				} else {
					log.Printf("✅ [CONSUMER] 工作器 %d 消息处理成功", workerID)
				}
			}
		}
	}
}

func processMessage(ctx context.Context, workerID int, message string, cfg *config.Config, workorderService *service.WorkOrderCallbackService) error {
	// 解析通知消息 (Redis队列中的格式)
	var notification CallbackNotification
	if err := json.Unmarshal([]byte(message), &notification); err != nil {
		return fmt.Errorf("解析通知消息JSON失败: %w", err)
	}

	provider := notification.Provider
	callbackID := notification.ID

	fmt.Printf("🔄 工作器 %d 处理来自 %s 的回调 (ID: %s)\n", workerID, provider, callbackID)
	log.Printf("🔄 [CONSUMER] 工作器 %d 处理来自 %s 的回调 (ID: %s)", workerID, provider, callbackID)

	// 创建带超时的上下文
	processCtx, cancel := context.WithTimeout(ctx, cfg.Consumer.WorkerTimeout)
	defer cancel()

	// 1. 从数据库获取完整的回调原始数据
	rawData, err := getRawCallbackData(processCtx, cfg, callbackID)
	if err != nil {
		return fmt.Errorf("获取回调原始数据失败: %w", err)
	}

	// 2. 统一转发到主系统统一回调端点，由主系统智能分发
	fmt.Printf("🔁 工作器 %d 统一转发到主服务: %s\n", workerID, callbackID)
	err = forwardUnifiedCallback(processCtx, cfg, rawData)

	if err != nil {
		return fmt.Errorf("转发回调到主系统失败: %w", err)
	}

	// 3. 标记为已处理
	err = markAsProcessed(processCtx, cfg, callbackID)
	if err != nil {
		// 不返回错误，因为业务逻辑已经处理成功
		fmt.Printf("⚠️ 工作器 %d 标记已处理失败: %s, %v\n", workerID, callbackID, err)
	}

	fmt.Printf("✅ 工作器 %d 成功处理 %s 回调 (ID: %s)\n", workerID, provider, callbackID)
	fmt.Printf("✅ [CONSUMER] 工作器 %d 成功处理 %s 回调 (ID: %s)", workerID, provider, callbackID)

	// 更新统计信息
	statsLock.Lock()
	consumerStats.ProcessedCount++
	consumerStats.LastProcessedAt = time.Now()
	statsLock.Unlock()

	return nil
}

// getRawCallbackData 从数据库获取原始回调数据
func getRawCallbackData(ctx context.Context, cfg *config.Config, callbackID string) (*CallbackRawData, error) {
	// 连接数据库
	db, err := sql.Open("postgres", cfg.Database.URL)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	query := `
		SELECT id, provider, raw_body, headers, client_ip,
		       received_at, processed, created_at
		FROM callback_raw_data
		WHERE id = $1
	`

	row := db.QueryRowContext(ctx, query, callbackID)

	var data CallbackRawData
	var headersJSON []byte

	err = row.Scan(
		&data.ID,
		&data.Provider,
		&data.RawBody,
		&headersJSON,
		&data.ClientIP,
		&data.ReceivedAt,
		&data.Processed,
		&data.CreatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("查询回调数据失败: %w", err)
	}

	// 反序列化headers
	err = json.Unmarshal(headersJSON, &data.Headers)
	if err != nil {
		return nil, fmt.Errorf("解析headers失败: %w", err)
	}

	return &data, nil
}

// isWorkOrderCallback 检查是否为工单回调
// 根据三个供应商的工单回调特征进行精确判断，其他一律视为物流回调
func isWorkOrderCallback(data *CallbackRawData) bool {
	if data == nil || data.RawBody == "" {
		return false
	}

	var rawMap map[string]interface{}
	if err := json.Unmarshal([]byte(data.RawBody), &rawMap); err != nil {
		return false
	}

	// 根据供应商检查对应的工单回调特征
	switch data.Provider {
	case "yuntong":
		// 云通工单回调特征：RequestType = 105
		if requestType, ok := rawMap["RequestType"].(float64); ok && requestType == 105 {
			return true
		}
		if requestType, ok := rawMap["RequestType"].(string); ok && requestType == "105" {
			return true
		}

		// 检查RequestData内部是否包含工单特征字段
		if requestData, ok := rawMap["RequestData"].(string); ok {
			var innerData map[string]interface{}
			if err := json.Unmarshal([]byte(requestData), &innerData); err == nil {
				if dataArray, ok := innerData["Data"].([]interface{}); ok && len(dataArray) > 0 {
					if workOrderData, ok := dataArray[0].(map[string]interface{}); ok {
						// 检查工单特征字段：ComplaintNumber, ComplaintType, ResultType
						if _, hasComplaintNumber := workOrderData["ComplaintNumber"]; hasComplaintNumber {
							return true
						}
						if _, hasComplaintType := workOrderData["ComplaintType"]; hasComplaintType {
							return true
						}
						if _, hasResultType := workOrderData["ResultType"]; hasResultType {
							return true
						}
					}
				}
			}
		}
		return false

	case "kuaidi100":
		// 快递100工单回调特征：包含workorderId且没有物流相关字段
		// 检查顶层字段
		if _, hasWorkOrderId := rawMap["workorderId"]; hasWorkOrderId {
			// 确认不是物流回调（物流回调通常包含这些字段）
			logisticsFields := []string{"LogisticCode", "logisticCode", "ShipperCode", "shipperCode", "State", "state"}
			for _, field := range logisticsFields {
				if _, exists := rawMap[field]; exists {
					return false // 包含物流字段，不是工单回调
				}
			}
			return true
		}

		// 检查param内部JSON
		if paramStr, ok := rawMap["param"].(string); ok {
			var paramMap map[string]interface{}
			if err := json.Unmarshal([]byte(paramStr), &paramMap); err == nil {
				if dataObj, ok := paramMap["data"].(map[string]interface{}); ok {
					if _, hasWorkOrderId := dataObj["workorderId"]; hasWorkOrderId {
						// 确认不是物流回调
						logisticsFields := []string{"LogisticCode", "logisticCode", "ShipperCode", "shipperCode", "State", "state"}
						for _, field := range logisticsFields {
							if _, exists := dataObj[field]; exists {
								return false
							}
						}
						return true
					}
				}
			}
		}
		return false

	case "yida":
		// 易达工单回调特征：包含taskNo, status, type字段的组合
		hasTaskNo := false
		hasStatus := false
		hasType := false

		if _, exists := rawMap["taskNo"]; exists {
			hasTaskNo = true
		}
		if _, exists := rawMap["status"]; exists {
			hasStatus = true
		}
		if _, exists := rawMap["type"]; exists {
			hasType = true
		}

		// 只有同时包含这三个字段才认为是工单回调
		if hasTaskNo && hasStatus && hasType {
			return true
		}
		return false

	default:
		// 其他供应商一律视为物流回调
		return false
	}
}

// forwardWorkOrderCallback 转发工单回调到主系统
func forwardWorkOrderCallback(ctx context.Context, cfg *config.Config, data *CallbackRawData) error {
	// 获取工单回调端点
	var endpoint string
	if cfg.WorkOrder.Enabled && cfg.WorkOrder.ProviderEndpoints != nil {
		if ep, exists := cfg.WorkOrder.ProviderEndpoints[data.Provider]; exists {
			endpoint = ep
		} else {
			endpoint = fmt.Sprintf("/api/v1/callbacks/workorders/%s", data.Provider)
		}
	} else {
		endpoint = fmt.Sprintf("/api/v1/callbacks/workorders/%s", data.Provider)
	}

	url := fmt.Sprintf("%s%s", cfg.MainSystem.URL, endpoint)
	return doForwardRequest(ctx, cfg, url, data)
}

// forwardLogisticsCallback 转发物流回调到主系统
func forwardLogisticsCallback(ctx context.Context, cfg *config.Config, data *CallbackRawData) error {
	// 获取物流回调端点
	var endpoint string
	if cfg.Providers.Kuaidi100.Enabled && data.Provider == "kuaidi100" {
		endpoint = cfg.Providers.Kuaidi100.APIEndpoint
	} else if cfg.Providers.Yida.Enabled && data.Provider == "yida" {
		endpoint = cfg.Providers.Yida.APIEndpoint
	} else if cfg.Providers.Yuntong.Enabled && data.Provider == "yuntong" {
		endpoint = cfg.Providers.Yuntong.APIEndpoint
	} else {
		endpoint = fmt.Sprintf("/api/v1/callbacks/%s", data.Provider)
	}

	url := fmt.Sprintf("%s%s", cfg.MainSystem.URL, endpoint)
	return doForwardRequest(ctx, cfg, url, data)
}

// forwardUnifiedCallback 统一转发至主系统统一回调端点，由主系统进行智能分发
func forwardUnifiedCallback(ctx context.Context, cfg *config.Config, data *CallbackRawData) error {
	url := fmt.Sprintf("%s/api/v1/callbacks/%s", cfg.MainSystem.URL, data.Provider)
	return doForwardRequest(ctx, cfg, url, data)
}

// doForwardRequest 执行转发请求
func doForwardRequest(ctx context.Context, cfg *config.Config, url string, data *CallbackRawData) error {
	// 创建HTTP客户端
	client := &http.Client{
		Timeout: cfg.MainSystem.Timeout,
	}

	// 使用原始body作为请求体
	req, err := http.NewRequestWithContext(ctx, "POST", url, strings.NewReader(data.RawBody))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	originalContentType := ""
	for key, value := range data.Headers {
		// 跳过可能有问题的头
		if key == "Content-Length" || key == "Transfer-Encoding" {
			continue
		}
		if key == "Content-Type" {
			originalContentType = value
		}
		req.Header.Set(key, value)
	}

	// 🔥 修复：保持原始Content-Type，不要强制设置为JSON
	if originalContentType == "" {
		req.Header.Set("Content-Type", "application/json")
	}
	req.Header.Set("X-Forwarded-For", data.ClientIP)
	req.Header.Set("X-Callback-ID", data.ID)

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("主系统返回错误状态码: %d", resp.StatusCode)
	}

	return nil
}

// markAsProcessed 标记为已处理
func markAsProcessed(ctx context.Context, cfg *config.Config, callbackID string) error {
	// 连接数据库
	db, err := sql.Open("postgres", cfg.Database.URL)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %w", err)
	}
	defer db.Close()

	query := `UPDATE callback_raw_data SET processed = true WHERE id = $1`
	_, err = db.ExecContext(ctx, query, callbackID)
	return err
}

func handleFailedMessage(ctx context.Context, rdb *redis.Client, cfg *config.Config, message string, processingErr error) error {
	// 解析消息获取重试信息
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(message), &data); err != nil {
		return fmt.Errorf("解析失败消息JSON失败: %w", err)
	}

	// 获取当前重试次数
	retryCount, _ := data["retry_count"].(float64)
	retryCount++

	// 检查是否超过最大重试次数
	if int(retryCount) > cfg.Consumer.MaxRetries {
		fmt.Printf("⚠️ 消息超过最大重试次数(%d)，移入死信队列\n", cfg.Consumer.MaxRetries)

		// 如果配置了死信队列
		if cfg.Recovery.DeadLetterQueue.Enabled {
			data["error"] = processingErr.Error()
			data["failed_at"] = time.Now().Unix()

			deadMessage, _ := json.Marshal(data)
			return rdb.LPush(ctx, cfg.Recovery.DeadLetterQueue.QueueName, deadMessage).Err()
		}
		return nil
	}

	// 更新重试信息
	data["retry_count"] = retryCount
	data["last_error"] = processingErr.Error()
	data["retry_at"] = time.Now().Add(cfg.Consumer.RetryDelay).Unix()

	// 序列化消息
	retryMessage, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化重试消息失败: %w", err)
	}

	// 延迟重试 (使用延迟队列或定时任务)
	if cfg.Redis.DelayedQueueName != "" {
		// 使用延迟队列
		return rdb.LPush(ctx, cfg.Redis.DelayedQueueName, retryMessage).Err()
	} else {
		// 直接重新放入队列
		time.Sleep(cfg.Consumer.RetryDelay)
		return rdb.LPush(ctx, cfg.Redis.QueueName, retryMessage).Err()
	}
}

// getWorkOrderCallbackEndpoint 获取工单回调端点
func (c *SimpleCallbackConsumer) getWorkOrderCallbackEndpoint(provider string) string {
	switch provider {
	case "kuaidi100":
		return "/api/v1/callbacks/workorders/kuaidi100"
	case "yida":
		return "/api/v1/callbacks/workorders/yida"
	case "yuntong":
		return "/api/v1/callbacks/workorders/yuntong"
	default:
		return fmt.Sprintf("/api/v1/callbacks/workorders/%s", provider)
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// initLogger 初始化日志
func initLogger(cfg *config.Config) (*zap.Logger, error) {
	level := zapcore.InfoLevel
	switch cfg.Logging.Level {
	case "debug":
		level = zapcore.DebugLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	}

	config := zap.Config{
		Level:       zap.NewAtomicLevelAt(level),
		Development: cfg.Development.Debug,
		Encoding:    cfg.Logging.Format,
		EncoderConfig: zapcore.EncoderConfig{
			TimeKey:        "timestamp",
			LevelKey:       "level",
			NameKey:        "logger",
			CallerKey:      "caller",
			MessageKey:     "msg",
			StacktraceKey:  "stacktrace",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		},
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}

	return config.Build()
}

// ConsumerStats 消费者统计信息
type ConsumerStats struct {
	WorkerCount     int       `json:"worker_count"`
	QueueName       string    `json:"queue_name"`
	QueueLength     int64     `json:"queue_length"`
	ProcessedCount  int64     `json:"processed_count"`
	FailedCount     int64     `json:"failed_count"`
	StartTime       time.Time `json:"start_time"`
	LastProcessedAt time.Time `json:"last_processed_at,omitempty"`
}

// 全局统计变量
var (
	consumerStats = &ConsumerStats{
		StartTime: time.Now(),
	}
	statsLock sync.RWMutex
)

// getSuccessRate 计算成功率
func getSuccessRate(processed, failed int64) float64 {
	total := processed + failed
	if total == 0 {
		return 0.0
	}
	return float64(processed) / float64(total) * 100.0
}
