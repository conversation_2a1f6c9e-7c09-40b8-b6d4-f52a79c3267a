package repository

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/lib/pq"
	"github.com/your-org/go-kuaidi/internal/database"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
	"go.uber.org/zap"
)

// OrderStatusUpdateRequest 订单状态更新请求
type OrderStatusUpdateRequest struct {
	OrderNo    string                 `json:"order_no"`
	NewStatus  string                 `json:"new_status"`
	UpdateTime time.Time              `json:"update_time"`
	Provider   string                 `json:"provider"`
	Extra      map[string]interface{} `json:"extra,omitempty"`
}

// OrderBillingUpdateRequest 订单计费更新请求
type OrderBillingUpdateRequest struct {
	OrderNo       string            `json:"order_no"`
	Weight        float64           `json:"weight"`
	Volume        float64           `json:"volume,omitempty"`
	ChargedWeight float64           `json:"charged_weight,omitempty"`
	TotalFee      float64           `json:"total_fee"`
	FeeDetails    []model.FeeDetail `json:"fee_details,omitempty"`
	Provider      string            `json:"provider"`
	UpdateTime    time.Time         `json:"update_time"`
}

// OrderPickupUpdateRequest 订单揽收信息更新请求
type OrderPickupUpdateRequest struct {
	OrderNo      string `json:"order_no"`
	CourierName  string `json:"courier_name,omitempty"`
	CourierPhone string `json:"courier_phone,omitempty"`
	CourierCode  string `json:"courier_code,omitempty"`
	StationName  string `json:"station_name,omitempty"`
	PickupCode   string `json:"pickup_code,omitempty"`
}

// OrderWeightUpdateRequest 订单重量体积信息更新请求
type OrderWeightUpdateRequest struct {
	OrderNo        string  `json:"order_no"`
	ActualWeight   float64 `json:"actual_weight,omitempty"`
	ActualVolume   float64 `json:"actual_volume,omitempty"`
	ChargedWeight  float64 `json:"charged_weight,omitempty"`
	OverweightFee  float64 `json:"overweight_fee,omitempty"`
	UnderweightFee float64 `json:"underweight_fee,omitempty"`
}

// AdminStatisticsFilter 管理员统计过滤器
type AdminStatisticsFilter struct {
	UserID        string
	Status        string
	Provider      string
	BillingStatus string
	StartTime     string
	EndTime       string
}

// OrderRepository 订单仓库接口
type OrderRepository interface {
	// Save 保存订单记录
	Save(ctx context.Context, order *model.OrderRecord) error

	// Update 更新订单记录
	Update(ctx context.Context, order *model.OrderRecord) error

	// FindByID 根据ID查询订单记录
	FindByID(ctx context.Context, id int64) (*model.OrderRecord, error)

	// FindByOrderNoOrTrackingNo 根据订单号或运单号查询订单记录
	FindByOrderNoOrTrackingNo(ctx context.Context, orderNo, trackingNo string) (*model.OrderRecord, error)

	// FindByCustomerOrderNo 根据客户订单号查询订单记录
	FindByCustomerOrderNo(ctx context.Context, customerOrderNo string) (*model.OrderRecord, error)
	// 🔥 新增：根据客户订单号模式查询订单记录（支持LIKE查询，用于处理订单号后缀的情况）
	FindByCustomerOrderNoPattern(ctx context.Context, pattern string) (*model.OrderRecord, error)

	// List 查询订单记录列表
	List(ctx context.Context, offset, limit int) ([]*model.OrderRecord, error)

	// ListWithFilter 根据条件查询订单列表
	ListWithFilter(ctx context.Context, req *model.OrderListRequest) ([]*model.OrderListItem, int64, error)

	// 🔥 新增：优化版订单列表查询
	ListWithFilterOptimized(ctx context.Context, req *model.OrderListRequest) ([]*model.OrderListItem, int64, error)

	// 🔥 新增：游标分页订单列表查询（针对列表项优化）
	ListWithFilterCursorPagination(ctx context.Context, req *model.OrderListRequest, cursor string) ([]*model.OrderListItem, string, error)

	// GetOrderStatistics 获取订单统计信息
	GetOrderStatistics(ctx context.Context, userID string) (*model.OrderStatistics, error)

	// 回调系统需要的方法
	// FindByCustomerOrderNoAndProvider 根据客户订单号和供应商查询订单记录
	FindByCustomerOrderNoAndProvider(ctx context.Context, customerOrderNo, provider string) (*model.OrderRecord, error)
	// FindByOrderNo 根据订单号查询订单记录
	FindByOrderNo(ctx context.Context, orderNo string) (*model.OrderRecord, error)
	// FindByPlatformOrderNoAndProvider 根据平台订单号和供应商查询订单记录（用于快递100等特殊情况）
	FindByPlatformOrderNoAndProvider(ctx context.Context, platformOrderNo, provider string) (*model.OrderRecord, error)
	// UpdateOrderStatus 更新订单状态
	UpdateOrderStatus(ctx context.Context, req *OrderStatusUpdateRequest) error
	// UpdateOrderBilling 更新订单计费信息
	UpdateOrderBilling(ctx context.Context, req *OrderBillingUpdateRequest) error
	// UpdateOrderActualFeeOnly 只更新订单的actual_fee字段，避免覆盖其他字段
	UpdateOrderActualFeeOnly(ctx context.Context, req *OrderBillingUpdateRequest) error
	// UpdateOrderPickupInfo 只更新订单的揽收信息字段，避免覆盖其他字段
	UpdateOrderPickupInfo(ctx context.Context, req *OrderPickupUpdateRequest) error
	// UpdateOrderTrackingNo 只更新订单的运单号字段，避免覆盖其他字段
	UpdateOrderTrackingNo(ctx context.Context, orderNo, trackingNo string) error
	// UpdateOrderWeightInfo 只更新订单的重量体积信息字段，避免覆盖其他字段
	UpdateOrderWeightInfo(ctx context.Context, req *OrderWeightUpdateRequest) error

	// 生成客户订单号
	GenerateCustomerOrderNo(ctx context.Context, provider string) (string, error)

	// 管理员专用方法
	// AdminListWithFilter 管理员订单列表查询（跨用户）
	AdminListWithFilter(ctx context.Context, req *model.AdminOrderListRequest) ([]*model.AdminOrderListItem, int64, error)
	// UpdateStatus 更新订单状态（管理员专用）
	UpdateStatus(ctx context.Context, orderID int64, newStatus string) error
	// BatchUpdateStatus 批量更新订单状态
	BatchUpdateStatus(ctx context.Context, orderIDs []int64, newStatus string) error
	// GetAdminStatistics 获取管理员统计数据
	GetAdminStatistics(ctx context.Context, filter *AdminStatisticsFilter) (*model.AdminOrderStatistics, error)
	// UpdatePriceValidation 更新订单的价格验证结果
	UpdatePriceValidation(ctx context.Context, orderID int64, validation *model.PriceValidationResult) error
	// BatchUpdatePriceValidation 批量更新价格验证结果
	BatchUpdatePriceValidation(ctx context.Context, results []*model.PriceValidationResult) error

	// 失败订单相关方法
	// CreateFailedOrder 创建失败订单记录
	CreateFailedOrder(ctx context.Context, order *model.OrderRecord) error
	// GetFailedOrdersByUser 获取用户的失败订单
	GetFailedOrdersByUser(ctx context.Context, userID string, limit, offset int) ([]*model.OrderRecord, error)
	// GetFailedOrderDetail 获取失败订单详情
	GetFailedOrderDetail(ctx context.Context, orderNo string, userID string) (*model.OrderRecord, error)
	// GetFailureStatistics 获取失败订单统计
	GetFailureStatistics(ctx context.Context, userID string, startTime time.Time) (*model.FailureStatistics, error)
	// DeleteExpiredFailedOrders 删除过期的失败订单
	DeleteExpiredFailedOrders(ctx context.Context, cutoffTime time.Time) (int, error)
	// 🔥 新增：删除失败订单
	DeleteFailedOrder(ctx context.Context, orderNo string, userID string) error

	// 🔥 新增：执行原生SQL查询（用于幂等性检查等特殊需求）
	ExecRawSQL(ctx context.Context, query string, args ...interface{}) error
	QueryRawSQL(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error)
	QueryRawSQLCount(ctx context.Context, query string, args ...interface{}) (int64, error)
	QueryRawSQLOrders(ctx context.Context, query string, args ...interface{}) ([]*model.OrderRecord, error)

	// 🔥 平台订单号相关方法
	FindByPlatformOrderNo(ctx context.Context, platformOrderNo string, userID string) (*model.OrderRecord, error)
	FindByTrackingNo(ctx context.Context, trackingNo string) (*model.OrderRecord, error)

	// 🚀 性能优化：单一查询支持多种标识符类型
	FindByAnyIdentifierOptimized(ctx context.Context, identifier string, userID string) (*model.OrderRecord, error)

	// 🚀 性能优化：游标分页查询，替代OFFSET分页
	ListWithCursorPagination(ctx context.Context, userID string, cursor string, limit int) ([]*model.OrderRecord, string, error)

	// 🚀 性能优化：轻量级查询，只返回必要字段
	FindByIdLightweight(ctx context.Context, id int64) (*model.OrderRecord, error)

	// 🔥 新增：超时保护相关方法
	FindTimeoutCancellingOrders(ctx context.Context, provider string, timeout time.Duration) ([]*model.OrderRecord, error)
	CountCancellingOrdersByProvider(ctx context.Context, provider string) (int, error)
	CountTimeoutCancellingOrdersByProvider(ctx context.Context, provider string, timeout time.Duration) (int, error)
}

// PostgresOrderRepository PostgreSQL实现的订单仓库
type PostgresOrderRepository struct {
	db        *sql.DB
	logger    *zap.Logger
	optimizer *database.QueryOptimizer // 🚀 新增：查询优化器
}

// NewPostgresOrderRepository 创建PostgreSQL订单仓库
func NewPostgresOrderRepository(db *sql.DB) *PostgresOrderRepository {
	logger, _ := zap.NewProduction()

	// 🚀 初始化查询优化器
	optimizer := database.NewQueryOptimizer(db, logger, nil)

	return &PostgresOrderRepository{
		db:        db,
		logger:    logger,
		optimizer: optimizer,
	}
}

// Save 保存订单记录
func (r *PostgresOrderRepository) Save(ctx context.Context, order *model.OrderRecord) error {
	query := `
		INSERT INTO order_records (
			platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, actual_fee, insurance_fee,
			overweight_fee, underweight_fee, weight_adjustment_reason,
			billing_status, order_volume, actual_weight, actual_volume,
			charged_weight, sender_info, receiver_info, package_info, request_data,
			response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			price_validation_provider_price, price_validation_system_price,
			price_validation_profit_status, price_validation_query_status,
			price_validation_query_time, price_validation_error_message,
			price_validation_supported
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16,
			$17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31,
			$32, $33, $34, $35, $36, $37, $38)
		RETURNING id
	`

	var id int64

	err := r.db.QueryRowContext(
		ctx, query,
		order.PlatformOrderNo,
		order.CustomerOrderNo,
		order.OrderNo,
		order.TrackingNo,
		order.ExpressType,
		order.ProductType,
		order.Provider,
		order.Status,
		order.Weight,
		order.Price,
		order.ActualFee,
		order.InsuranceFee,
		order.OverweightFee,
		order.UnderweightFee,
		order.WeightAdjustmentReason,
		order.BillingStatus,
		order.OrderVolume,
		order.ActualWeight,
		order.ActualVolume,
		order.ChargedWeight,
		order.SenderInfo,
		order.ReceiverInfo,
		order.PackageInfo,
		order.RequestData,
		order.ResponseData,
		order.OrderCode,
		order.CreatedAt,
		order.UpdatedAt,
		order.TaskId,
		order.PollToken,
		order.UserID,
		order.PriceValidationProviderPrice,
		order.PriceValidationSystemPrice,
		order.PriceValidationProfitStatus,
		order.PriceValidationQueryStatus,
		order.PriceValidationQueryTime,
		order.PriceValidationErrorMessage,
		order.PriceValidationSupported,
	).Scan(&id)

	if err != nil {
		return fmt.Errorf("保存订单记录失败: %w", err)
	}

	order.ID = id
	return nil
}

// Update 更新订单记录
func (r *PostgresOrderRepository) Update(ctx context.Context, order *model.OrderRecord) error {
	query := `
		UPDATE order_records SET
			customer_order_no = $1,
			order_no = $2,
			tracking_no = $3,
			express_type = $4,
			product_type = $5,
			provider = $6,
			status = $7,
			weight = $8,
			price = $9,
			actual_fee = $10,
			insurance_fee = $11,
			overweight_fee = $12,
			underweight_fee = $13,
			weight_adjustment_reason = $14,
			billing_status = $15,
			order_volume = $16,
			actual_weight = $17,
			actual_volume = $18,
			charged_weight = $19,
			sender_info = $20,
			receiver_info = $21,
			package_info = $22,
			request_data = $23,
			response_data = $24,
			order_code = $25,
			updated_at = $26,
			task_id = $27,
			poll_token = $28,
			user_id = $29,
			courier_name = $30,
			courier_phone = $31,
			courier_code = $32,
			station_name = $33,
			pickup_code = $34
		WHERE id = $35
	`

	result, err := r.db.ExecContext(
		ctx, query,
		order.CustomerOrderNo,
		order.OrderNo,
		order.TrackingNo,
		order.ExpressType,
		order.ProductType,
		order.Provider,
		order.Status,
		order.Weight,
		order.Price,
		order.ActualFee,
		order.InsuranceFee,
		order.OverweightFee,
		order.UnderweightFee,
		order.WeightAdjustmentReason,
		order.BillingStatus,
		order.OrderVolume,
		order.ActualWeight,
		order.ActualVolume,
		order.ChargedWeight,
		order.SenderInfo,
		order.ReceiverInfo,
		order.PackageInfo,
		order.RequestData,
		order.ResponseData,
		order.OrderCode,
		util.NowBeijing(),
		order.TaskId,
		order.PollToken,
		order.UserID,
		order.CourierName,
		order.CourierPhone,
		order.CourierCode,
		order.StationName,
		order.PickupCode,
		order.ID,
	)

	if err != nil {
		return fmt.Errorf("更新订单记录失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return errors.New("订单记录不存在")
	}

	return nil
}

// FindByID 根据ID查询订单记录
func (r *PostgresOrderRepository) FindByID(ctx context.Context, id int64) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			price_validation_provider_price,
			price_validation_system_price,
			price_validation_profit_status,
			price_validation_query_status,
			price_validation_query_time,
			price_validation_error_message,
			price_validation_supported
		FROM order_records
		WHERE id = $1
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData, &order.OrderCode,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
		&order.PriceValidationProviderPrice,
		&order.PriceValidationSystemPrice,
		&order.PriceValidationProfitStatus,
		&order.PriceValidationQueryStatus,
		&order.PriceValidationQueryTime,
		&order.PriceValidationErrorMessage,
		&order.PriceValidationSupported,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("订单记录不存在")
		}
		return nil, fmt.Errorf("查询订单记录失败: %w", err)
	}

	return &order, nil
}

// 🗑️ DEPRECATED: FindByOrderNoOrTrackingNo 已废弃 - 使用 ConcurrentSafeOrderFinder 替代
// 这个方法有严重的查询逻辑错误，会导致订单查找不准确
func (r *PostgresOrderRepository) FindByOrderNoOrTrackingNo(ctx context.Context, orderNo, trackingNo string) (*model.OrderRecord, error) {
	// 🚨 警告：此方法已废弃，请使用 ConcurrentSafeOrderFinder
	return nil, fmt.Errorf("DEPRECATED: FindByOrderNoOrTrackingNo 方法已废弃，请使用 ConcurrentSafeOrderFinder")
}

// FindByCustomerOrderNo 根据客户订单号查询订单记录
func (r *PostgresOrderRepository) FindByCustomerOrderNo(ctx context.Context, customerOrderNo string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight, COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code
		FROM order_records
		WHERE customer_order_no = $1
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, customerOrderNo).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("订单记录不存在")
		}
		return nil, fmt.Errorf("查询订单记录失败: %w", err)
	}

	return &order, nil
}

// 🔥 新增：根据客户订单号模式查询订单记录（用于模糊匹配，处理订单号后缀的情况）
func (r *PostgresOrderRepository) FindByCustomerOrderNoPattern(ctx context.Context, pattern string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight, COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code
		FROM order_records
		WHERE customer_order_no LIKE $1
		ORDER BY created_at DESC
		LIMIT 1
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, pattern).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("订单记录不存在")
		}
		return nil, fmt.Errorf("查询订单记录失败: %w", err)
	}

	return &order, nil
}

// List 查询订单记录列表
func (r *PostgresOrderRepository) List(ctx context.Context, offset, limit int) ([]*model.OrderRecord, error) {
	query := `
		SELECT
			id, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at, task_id, poll_token
		FROM order_records
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := r.db.QueryContext(ctx, query, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询订单记录列表失败: %w", err)
	}
	defer rows.Close()

	var orders []*model.OrderRecord
	for rows.Next() {
		var order model.OrderRecord
		err := rows.Scan(
			&order.ID, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
			&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
			&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
			&order.PackageInfo, &order.RequestData, &order.ResponseData,
			&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描订单记录失败: %w", err)
		}
		orders = append(orders, &order)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代订单记录失败: %w", err)
	}

	return orders, nil
}

// ListWithFilter 根据条件查询订单列表
func (r *PostgresOrderRepository) ListWithFilter(ctx context.Context, req *model.OrderListRequest) ([]*model.OrderListItem, int64, error) {
	// 构建WHERE条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	// 添加用户ID过滤（必须的安全过滤）
	if req.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, req.UserID)
		argIndex++
	}

	// 添加其他过滤条件
	if req.Status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, req.Status)
		argIndex++
	}

	if req.ExpressType != "" {
		conditions = append(conditions, fmt.Sprintf("express_type = $%d", argIndex))
		args = append(args, req.ExpressType)
		argIndex++
	}

	// 重量异常筛选
	if req.WeightAnomaly != "" {
		if req.WeightAnomaly == "overweight" {
			// 超重：实际重量 > 下单重量 且 实收费用 > 预收费用
			// 只有当实际重量和实际费用都有有效值时才判断
			conditions = append(conditions, "actual_weight > 0 AND actual_fee > 0 AND actual_weight > weight AND actual_fee > price")
		} else if req.WeightAnomaly == "underweight" {
			// 超轻：实际重量 < 下单重量 且 实收费用 < 预收费用
			// 只有当实际重量和实际费用都有有效值时才判断
			conditions = append(conditions, "actual_weight > 0 AND actual_fee > 0 AND actual_weight < weight AND actual_fee < price")
		}
	}

	if req.Provider != "" {
		conditions = append(conditions, fmt.Sprintf("provider = $%d", argIndex))
		args = append(args, req.Provider)
		argIndex++
	}

	if req.CustomerOrderNo != "" {
		conditions = append(conditions, fmt.Sprintf("customer_order_no ILIKE $%d", argIndex))
		args = append(args, "%"+req.CustomerOrderNo+"%")
		argIndex++
	}

	if req.OrderNo != "" {
		conditions = append(conditions, fmt.Sprintf("order_no ILIKE $%d", argIndex))
		args = append(args, "%"+req.OrderNo+"%")
		argIndex++
	}

	if req.TrackingNo != "" {
		conditions = append(conditions, fmt.Sprintf("tracking_no ILIKE $%d", argIndex))
		args = append(args, "%"+req.TrackingNo+"%")
		argIndex++
	}

	// 🔥 新增：批量运单号查询支持
	if len(req.TrackingNos) > 0 {
		// 使用PostgreSQL的ANY操作符进行批量查询，性能优于多个OR条件
		conditions = append(conditions, fmt.Sprintf("tracking_no = ANY($%d)", argIndex))
		args = append(args, pq.Array(req.TrackingNos))
		argIndex++
	}

	// 时间范围过滤
	if req.StartTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, req.StartTime)
		argIndex++
	}

	if req.EndTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, req.EndTime)
		argIndex++
	}

	// 构建WHERE子句
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 构建ORDER BY子句
	orderClause := fmt.Sprintf("ORDER BY %s %s", req.SortBy, strings.ToUpper(req.SortOrder))

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM order_records %s", whereClause)
	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("查询订单总数失败: %w", err)
	}

	// 查询数据
	dataQuery := fmt.Sprintf(`
		SELECT
			id,
			COALESCE(platform_order_no, '') as platform_order_no,
			customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(billing_status, 'pending') as billing_status,
			sender_info, receiver_info,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			-- 🔥 企业级修复：添加task_id和poll_token字段
			COALESCE(task_id, '') as task_id,
			COALESCE(poll_token, '') as poll_token,
			-- 🔥 新增：从request_data中提取预约时间
			COALESCE(request_data->'pickup'->>'start_time', '') as pickup_start_time,
			COALESCE(request_data->'pickup'->>'end_time', '') as pickup_end_time,
			created_at, updated_at
		FROM order_records
		%s
		%s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderClause, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	// 🚀 使用查询优化器执行查询
	startTime := time.Now()
	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	queryDuration := time.Since(startTime)

	// 记录查询性能
	if queryDuration > 100*time.Millisecond {
		r.logger.Warn("订单列表查询较慢",
			zap.Duration("duration", queryDuration),
			zap.String("user_id", req.UserID),
			zap.Int("page", req.Page),
			zap.Int("page_size", req.PageSize))
	}

	if err != nil {
		return nil, 0, fmt.Errorf("查询订单列表失败: %w", err)
	}
	defer rows.Close()

	var items []*model.OrderListItem
	for rows.Next() {
		var item model.OrderListItem
		var senderInfo, receiverInfo string

		err := rows.Scan(
			&item.ID, &item.PlatformOrderNo, &item.CustomerOrderNo, &item.ProviderOrderNo, &item.TrackingNo,
			&item.ExpressType, &item.ProductType, &item.Provider, &item.Status,
			&item.Weight, &item.Price,
			&item.ActualFee, &item.InsuranceFee, &item.BillingStatus,
			&senderInfo, &receiverInfo,
			&item.OrderVolume, &item.ActualWeight, &item.ActualVolume, &item.ChargedWeight,
			&item.CourierName, &item.CourierPhone, &item.CourierCode,
			&item.StationName, &item.PickupCode,
			// 🔥 企业级修复：扫描task_id和poll_token字段
			&item.TaskId, &item.PollToken,
			// 🔥 新增：扫描预约时间字段
			&item.PickupStartTime, &item.PickupEndTime,
			&item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描订单记录失败: %w", err)
		}

		// 🔥 设置兼容性字段：OrderNo优先显示平台订单号
		if item.PlatformOrderNo != "" {
			item.OrderNo = item.PlatformOrderNo
		} else {
			item.OrderNo = item.ProviderOrderNo
		}

		// 处理寄件人和收件人信息摘要
		item.SenderInfo = extractAddressSummary(senderInfo)
		item.ReceiverInfo = extractAddressSummary(receiverInfo)

		// 设置状态描述
		item.StatusDesc = model.GetOrderStatusDesc(item.Status)

		// 设置供应商名称
		item.ProviderName = model.GetProviderName(item.Provider)

		// 设置快递公司名称
		item.ExpressName = getExpressName(item.ExpressType)

		// 🔥 修复：处理 timestamp without time zone 的时区问题
		// 数据库存储的是北京时间值，只需要添加时区标记，不需要转换
		item.CreatedAt = util.EnsureBeijingTimezone(item.CreatedAt)
		item.UpdatedAt = util.EnsureBeijingTimezone(item.UpdatedAt)

		items = append(items, &item)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("迭代订单记录失败: %w", err)
	}

	return items, total, nil
}

// 🔥 新增：优化版订单列表查询
func (r *PostgresOrderRepository) ListWithFilterOptimized(ctx context.Context, req *model.OrderListRequest) ([]*model.OrderListItem, int64, error) {
	// 🔥 性能优化策略：
	// 1. 精简字段查询：只查询列表必需的字段
	// 2. 优化WHERE条件：按索引友好的顺序排列
	// 3. 智能计数：小数据集直接计数，大数据集使用估算
	// 4. 覆盖索引：利用新创建的覆盖索引减少回表

	startTime := time.Now()

	// 构建优化的WHERE条件（按索引友好顺序）
	var conditions []string
	var args []interface{}
	argIndex := 1

	// 🔥 优化1：用户ID条件放在最前面（利用索引前缀）
	if req.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, req.UserID)
		argIndex++
	}

	// 🔥 优化2：状态条件紧随其后（利用复合索引）
	if req.Status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, req.Status)
		argIndex++
	}

	// 🔥 优化3：供应商条件（利用复合索引）
	if req.Provider != "" {
		conditions = append(conditions, fmt.Sprintf("provider = $%d", argIndex))
		args = append(args, req.Provider)
		argIndex++
	}

	// 🔥 优化4：快递类型条件（利用复合索引）
	if req.ExpressType != "" {
		conditions = append(conditions, fmt.Sprintf("express_type = $%d", argIndex))
		args = append(args, req.ExpressType)
		argIndex++
	}

	// 🔥 优化5：精确匹配条件（利用专用索引）
	exactMatchUsed := false
	if req.TrackingNo != "" && !strings.Contains(req.TrackingNo, "%") {
		// 精确匹配运单号
		conditions = append(conditions, fmt.Sprintf("tracking_no = $%d", argIndex))
		args = append(args, req.TrackingNo)
		argIndex++
		exactMatchUsed = true
	} else if req.TrackingNo != "" {
		// 模糊匹配运单号
		conditions = append(conditions, fmt.Sprintf("tracking_no ILIKE $%d", argIndex))
		args = append(args, "%"+req.TrackingNo+"%")
		argIndex++
	}

	if req.CustomerOrderNo != "" && !exactMatchUsed {
		if !strings.Contains(req.CustomerOrderNo, "%") {
			conditions = append(conditions, fmt.Sprintf("customer_order_no = $%d", argIndex))
			args = append(args, req.CustomerOrderNo)
		} else {
			conditions = append(conditions, fmt.Sprintf("customer_order_no ILIKE $%d", argIndex))
			args = append(args, "%"+req.CustomerOrderNo+"%")
		}
		argIndex++
	}

	if req.OrderNo != "" && !exactMatchUsed {
		if !strings.Contains(req.OrderNo, "%") {
			conditions = append(conditions, fmt.Sprintf("order_no = $%d", argIndex))
			args = append(args, req.OrderNo)
		} else {
			conditions = append(conditions, fmt.Sprintf("order_no ILIKE $%d", argIndex))
			args = append(args, "%"+req.OrderNo+"%")
		}
		argIndex++
	}

	// 🔥 优化6：批量运单号查询
	if len(req.TrackingNos) > 0 {
		conditions = append(conditions, fmt.Sprintf("tracking_no = ANY($%d)", argIndex))
		args = append(args, pq.Array(req.TrackingNos))
		argIndex++
	}

	// 🔥 优化7：时间范围条件（利用时间索引）
	if req.StartTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, req.StartTime)
		argIndex++
	}

	if req.EndTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, req.EndTime)
		argIndex++
	}

	// 🔥 优化8：重量异常筛选（利用专用索引）
	if req.WeightAnomaly != "" {
		if req.WeightAnomaly == "overweight" {
			conditions = append(conditions, "actual_weight > 0 AND actual_fee > 0 AND actual_weight > weight AND actual_fee > price")
		} else if req.WeightAnomaly == "underweight" {
			conditions = append(conditions, "actual_weight > 0 AND actual_fee > 0 AND actual_weight < weight AND actual_fee < price")
		}
	}

	// 构建WHERE子句
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 🔥 优化9：智能计数策略
	var total int64
	var err error

	// 对于简单查询，使用精确计数；对于复杂查询，使用估算
	if r.isSimpleQuery(req) {
		countQuery := fmt.Sprintf("SELECT COUNT(*) FROM order_records %s", whereClause)
		err = r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
		if err != nil {
			return nil, 0, fmt.Errorf("查询订单总数失败: %w", err)
		}
	} else {
		// 使用估算计数（对于复杂查询）
		total = r.estimateCount(ctx, whereClause, args)
	}

	// 🔥 优化10：精简字段查询（包含预约时间、揽件员信息与重量体积字段）
	selectFields := `
		id,
		COALESCE(platform_order_no, '') as platform_order_no,
		customer_order_no,
		order_no,
		tracking_no,
		express_type,
		product_type,
		provider,
		status,
		weight,
		price,
		COALESCE(actual_fee, 0) as actual_fee,
		COALESCE(billing_status, 'pending') as billing_status,
		-- 🔥 修复：添加揽件员信息字段
		COALESCE(courier_name, '') as courier_name,
		COALESCE(courier_phone, '') as courier_phone,
		COALESCE(courier_code, '') as courier_code,
		COALESCE(station_name, '') as station_name,
		COALESCE(pickup_code, '') as pickup_code,
		-- 🔥 修复：从request_data中提取预约时间
		COALESCE(request_data->'pickup'->>'start_time', '') as pickup_start_time,
		COALESCE(request_data->'pickup'->>'end_time', '') as pickup_end_time,
		-- 🔥 新增：重量体积信息（前端所需）
		COALESCE(order_volume, 0) as order_volume,
		COALESCE(actual_weight, 0) as actual_weight,
		COALESCE(actual_volume, 0) as actual_volume,
		COALESCE(charged_weight, 0) as charged_weight,
		created_at,
		updated_at`

	// 构建ORDER BY子句
	orderClause := fmt.Sprintf("ORDER BY %s %s", req.SortBy, strings.ToUpper(req.SortOrder))

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 🔥 优化11：使用覆盖索引查询
	dataQuery := fmt.Sprintf(`
		SELECT %s
		FROM order_records
		%s
		%s
		LIMIT $%d OFFSET $%d
	`, selectFields, whereClause, orderClause, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	// 执行查询并记录性能
	queryStart := time.Now()
	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	queryDuration := time.Since(queryStart)

	// 记录查询性能
	if queryDuration > 50*time.Millisecond {
		r.logger.Warn("订单列表优化查询较慢",
			zap.Duration("duration", queryDuration),
			zap.String("user_id", req.UserID),
			zap.Int("page", req.Page),
			zap.Int("page_size", req.PageSize),
			zap.Bool("exact_match", exactMatchUsed))
	}

	if err != nil {
		return nil, 0, fmt.Errorf("查询订单列表失败: %w", err)
	}
	defer rows.Close()

	var items []*model.OrderListItem
	for rows.Next() {
		var item model.OrderListItem

		// 🔥 修复：使用临时变量接收order_no字段，然后映射到ProviderOrderNo
		var orderNo string
		err := rows.Scan(
			&item.ID, &item.PlatformOrderNo, &item.CustomerOrderNo, &orderNo, &item.TrackingNo,
			&item.ExpressType, &item.ProductType, &item.Provider, &item.Status,
			&item.Weight, &item.Price, &item.ActualFee, &item.BillingStatus,
			// 🔥 修复：扫描揽件员信息字段
			&item.CourierName, &item.CourierPhone, &item.CourierCode,
			&item.StationName, &item.PickupCode,
			// 🔥 修复：扫描预约时间字段
			&item.PickupStartTime, &item.PickupEndTime,
			// ✅ 新增：扫描重量体积字段（与SELECT顺序一致）
			&item.OrderVolume, &item.ActualWeight, &item.ActualVolume, &item.ChargedWeight,
			&item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描订单记录失败: %w", err)
		}

		// 🔥 修复：设置ProviderOrderNo字段
		item.ProviderOrderNo = orderNo

		// 🔥 优化12：减少字符串处理和函数调用
		// 设置兼容性字段：OrderNo优先显示平台订单号
		if item.PlatformOrderNo != "" {
			item.OrderNo = item.PlatformOrderNo
		} else {
			item.OrderNo = item.ProviderOrderNo
		}

		// 设置状态描述
		item.StatusDesc = model.GetOrderStatusDesc(item.Status)

		// 设置供应商名称
		item.ProviderName = model.GetProviderName(item.Provider)

		// 设置快递公司名称
		item.ExpressName = getExpressName(item.ExpressType)

		// 处理时区
		item.CreatedAt = util.EnsureBeijingTimezone(item.CreatedAt)
		item.UpdatedAt = util.EnsureBeijingTimezone(item.UpdatedAt)

		items = append(items, &item)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("迭代订单记录失败: %w", err)
	}

	// 记录总体性能
	totalDuration := time.Since(startTime)
	r.logger.Debug("订单列表优化查询完成",
		zap.Duration("total_duration", totalDuration),
		zap.Duration("query_duration", queryDuration),
		zap.String("user_id", req.UserID),
		zap.Int64("total", total),
		zap.Int("items_count", len(items)))

	return items, total, nil
}

// 🔥 新增：判断是否为简单查询
func (r *PostgresOrderRepository) isSimpleQuery(req *model.OrderListRequest) bool {
	// 简单查询的定义：只有基本筛选条件，没有复杂的模糊查询或重量异常筛选
	complexConditions := 0

	// 模糊查询条件
	if req.TrackingNo != "" && strings.Contains(req.TrackingNo, "%") {
		complexConditions++
	}
	if req.CustomerOrderNo != "" && strings.Contains(req.CustomerOrderNo, "%") {
		complexConditions++
	}
	if req.OrderNo != "" && strings.Contains(req.OrderNo, "%") {
		complexConditions++
	}

	// 重量异常筛选
	if req.WeightAnomaly != "" {
		complexConditions++
	}

	// 批量查询
	if len(req.TrackingNos) > 10 {
		complexConditions++
	}

	// 如果复杂条件少于2个，认为是简单查询
	return complexConditions < 2
}

// 🔥 新增：估算计数（用于复杂查询）
func (r *PostgresOrderRepository) estimateCount(ctx context.Context, whereClause string, args []interface{}) int64 {
	// 对于复杂查询，使用统计信息估算
	// 这里可以根据实际情况实现更复杂的估算逻辑

	// 简单实现：使用EXPLAIN来获取估算行数
	explainQuery := fmt.Sprintf("EXPLAIN (FORMAT JSON) SELECT COUNT(*) FROM order_records %s", whereClause)

	rows, err := r.db.QueryContext(ctx, explainQuery, args...)
	if err != nil {
		// 如果估算失败，回退到精确计数
		countQuery := fmt.Sprintf("SELECT COUNT(*) FROM order_records %s", whereClause)
		var count int64
		err = r.db.QueryRowContext(ctx, countQuery, args...).Scan(&count)
		if err != nil {
			return 0
		}
		return count
	}
	defer rows.Close()

	// 解析EXPLAIN结果获取估算行数
	// 这里简化处理，实际可以解析JSON获取更准确的估算
	var count int64 = 1000 // 默认估算值

	return count
}

// 🔥 新增：游标分页订单列表查询（针对列表项优化）
func (r *PostgresOrderRepository) ListWithFilterCursorPagination(ctx context.Context, req *model.OrderListRequest, cursor string) ([]*model.OrderListItem, string, error) {
	// 🔥 游标分页优化策略：
	// 1. 使用created_at + id作为游标，确保唯一性和排序稳定性
	// 2. 避免OFFSET的性能问题
	// 3. 支持所有筛选条件
	// 4. 返回下一页游标

	startTime := time.Now()

	// 解析游标
	var cursorTime time.Time
	var cursorID int64
	var err error

	if cursor != "" {
		// 游标格式：timestamp_id
		parts := strings.Split(cursor, "_")
		if len(parts) == 2 {
			cursorTime, err = time.Parse(time.RFC3339, parts[0])
			if err != nil {
				return nil, "", fmt.Errorf("无效的游标时间格式: %w", err)
			}
			cursorID, err = strconv.ParseInt(parts[1], 10, 64)
			if err != nil {
				return nil, "", fmt.Errorf("无效的游标ID格式: %w", err)
			}
		} else {
			return nil, "", fmt.Errorf("无效的游标格式")
		}
	} else {
		// 如果没有游标，使用当前时间
		cursorTime = time.Now()
		cursorID = 0
	}

	// 构建WHERE条件（复用优化的条件构建逻辑）
	var conditions []string
	var args []interface{}
	argIndex := 1

	// 🔥 优化1：用户ID条件（必须）
	if req.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, req.UserID)
		argIndex++
	}

	// 🔥 优化2：游标条件（核心优化）
	if cursor != "" {
		// 使用复合游标条件：(created_at, id) < (cursor_time, cursor_id)
		conditions = append(conditions, fmt.Sprintf("(created_at, id) < ($%d, $%d)", argIndex, argIndex+1))
		args = append(args, cursorTime, cursorID)
		argIndex += 2
	}

	// 添加其他筛选条件
	if req.Status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, req.Status)
		argIndex++
	}

	if req.Provider != "" {
		conditions = append(conditions, fmt.Sprintf("provider = $%d", argIndex))
		args = append(args, req.Provider)
		argIndex++
	}

	if req.ExpressType != "" {
		conditions = append(conditions, fmt.Sprintf("express_type = $%d", argIndex))
		args = append(args, req.ExpressType)
		argIndex++
	}

	// 精确匹配条件
	if req.TrackingNo != "" && !strings.Contains(req.TrackingNo, "%") {
		conditions = append(conditions, fmt.Sprintf("tracking_no = $%d", argIndex))
		args = append(args, req.TrackingNo)
		argIndex++
	} else if req.TrackingNo != "" {
		conditions = append(conditions, fmt.Sprintf("tracking_no ILIKE $%d", argIndex))
		args = append(args, "%"+req.TrackingNo+"%")
		argIndex++
	}

	if req.CustomerOrderNo != "" {
		if !strings.Contains(req.CustomerOrderNo, "%") {
			conditions = append(conditions, fmt.Sprintf("customer_order_no = $%d", argIndex))
			args = append(args, req.CustomerOrderNo)
		} else {
			conditions = append(conditions, fmt.Sprintf("customer_order_no ILIKE $%d", argIndex))
			args = append(args, "%"+req.CustomerOrderNo+"%")
		}
		argIndex++
	}

	// 批量运单号查询
	if len(req.TrackingNos) > 0 {
		conditions = append(conditions, fmt.Sprintf("tracking_no = ANY($%d)", argIndex))
		args = append(args, pq.Array(req.TrackingNos))
		argIndex++
	}

	// 时间范围条件
	if req.StartTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, req.StartTime)
		argIndex++
	}

	if req.EndTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, req.EndTime)
		argIndex++
	}

	// 重量异常筛选
	if req.WeightAnomaly != "" {
		if req.WeightAnomaly == "overweight" {
			conditions = append(conditions, "actual_weight > 0 AND actual_fee > 0 AND actual_weight > weight AND actual_fee > price")
		} else if req.WeightAnomaly == "underweight" {
			conditions = append(conditions, "actual_weight > 0 AND actual_fee > 0 AND actual_weight < weight AND actual_fee < price")
		}
	}

	// 构建WHERE子句
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 🔥 优化3：精简字段查询（包含预约时间和揽件员信息）
	selectFields := `
		id,
		COALESCE(platform_order_no, '') as platform_order_no,
		customer_order_no,
		order_no,
		tracking_no,
		express_type,
		product_type,
		provider,
		status,
		weight,
		price,
		COALESCE(actual_fee, 0) as actual_fee,
		COALESCE(billing_status, 'pending') as billing_status,
		-- 🔥 修复：添加揽件员信息字段
		COALESCE(courier_name, '') as courier_name,
		COALESCE(courier_phone, '') as courier_phone,
		COALESCE(courier_code, '') as courier_code,
		COALESCE(station_name, '') as station_name,
		COALESCE(pickup_code, '') as pickup_code,
		-- 🔥 修复：从request_data中提取预约时间
		COALESCE(request_data->'pickup'->>'start_time', '') as pickup_start_time,
		COALESCE(request_data->'pickup'->>'end_time', '') as pickup_end_time,
		created_at,
		updated_at`

	// 🔥 优化4：游标分页查询（固定按created_at DESC, id DESC排序）
	dataQuery := fmt.Sprintf(`
		SELECT %s
		FROM order_records
		%s
		ORDER BY created_at DESC, id DESC
		LIMIT $%d
	`, selectFields, whereClause, argIndex)

	// 限制每页数量
	limit := req.PageSize
	if limit <= 0 || limit > 100 {
		limit = 20
	}
	args = append(args, limit+1) // +1用于判断是否有下一页

	// 执行查询
	queryStart := time.Now()
	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	queryDuration := time.Since(queryStart)

	if err != nil {
		return nil, "", fmt.Errorf("游标分页查询失败: %w", err)
	}
	defer rows.Close()

	var items []*model.OrderListItem
	for rows.Next() {
		var item model.OrderListItem

		// 🔥 修复：使用临时变量接收order_no字段，然后映射到ProviderOrderNo
		var orderNo string
		err := rows.Scan(
			&item.ID, &item.PlatformOrderNo, &item.CustomerOrderNo, &orderNo, &item.TrackingNo,
			&item.ExpressType, &item.ProductType, &item.Provider, &item.Status,
			&item.Weight, &item.Price, &item.ActualFee, &item.BillingStatus,
			// 🔥 修复：扫描揽件员信息字段
			&item.CourierName, &item.CourierPhone, &item.CourierCode,
			&item.StationName, &item.PickupCode,
			// 🔥 修复：扫描预约时间字段
			&item.PickupStartTime, &item.PickupEndTime,
			&item.CreatedAt, &item.UpdatedAt,
		)
		if err != nil {
			return nil, "", fmt.Errorf("扫描订单记录失败: %w", err)
		}

		// 🔥 修复：设置ProviderOrderNo字段
		item.ProviderOrderNo = orderNo

		// 设置兼容性字段
		if item.PlatformOrderNo != "" {
			item.OrderNo = item.PlatformOrderNo
		} else {
			item.OrderNo = item.ProviderOrderNo
		}

		// 设置描述字段
		item.StatusDesc = model.GetOrderStatusDesc(item.Status)
		item.ProviderName = model.GetProviderName(item.Provider)
		item.ExpressName = getExpressName(item.ExpressType)

		// 处理时区
		item.CreatedAt = util.EnsureBeijingTimezone(item.CreatedAt)
		item.UpdatedAt = util.EnsureBeijingTimezone(item.UpdatedAt)

		items = append(items, &item)
	}

	if err := rows.Err(); err != nil {
		return nil, "", fmt.Errorf("迭代订单记录失败: %w", err)
	}

	// 🔥 优化5：生成下一页游标
	var nextCursor string
	hasNext := len(items) > limit

	if hasNext {
		// 移除多查询的一条记录
		items = items[:limit]
		// 使用最后一条记录生成游标
		lastItem := items[len(items)-1]
		nextCursor = fmt.Sprintf("%s_%d", lastItem.CreatedAt.Format(time.RFC3339), lastItem.ID)
	}

	// 记录性能
	totalDuration := time.Since(startTime)
	r.logger.Debug("游标分页查询完成",
		zap.Duration("total_duration", totalDuration),
		zap.Duration("query_duration", queryDuration),
		zap.String("user_id", req.UserID),
		zap.Int("items_count", len(items)),
		zap.Bool("has_next", hasNext),
		zap.String("cursor", cursor),
		zap.String("next_cursor", nextCursor))

	return items, nextCursor, nil
}

// GetOrderStatistics 获取订单统计信息（用户版本，不包含敏感信息）
func (r *PostgresOrderRepository) GetOrderStatistics(ctx context.Context, userID string) (*model.OrderStatistics, error) {
	stats := &model.OrderStatistics{
		StatusCounts: make(map[string]int64),
	}

	// 构建用户过滤条件
	var userFilter string
	var args []interface{}
	if userID != "" {
		userFilter = "WHERE user_id = $1"
		args = append(args, userID)
	}

	// 查询总订单数
	totalQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM order_records
		%s
	`, userFilter)
	err := r.db.QueryRowContext(ctx, totalQuery, args...).Scan(&stats.TotalOrders)
	if err != nil {
		return nil, fmt.Errorf("查询总订单统计失败: %w", err)
	}

	// 查询今日订单数
	todayFilter := userFilter
	todayArgs := args
	if userID != "" {
		todayFilter = "WHERE user_id = $1 AND DATE(created_at) = CURRENT_DATE"
	} else {
		todayFilter = "WHERE DATE(created_at) = CURRENT_DATE"
	}

	todayQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM order_records
		%s
	`, todayFilter)
	err = r.db.QueryRowContext(ctx, todayQuery, todayArgs...).Scan(&stats.TodayOrders)
	if err != nil {
		return nil, fmt.Errorf("查询今日订单统计失败: %w", err)
	}

	// 查询各状态订单数量
	statusQuery := fmt.Sprintf(`
		SELECT status, COUNT(*)
		FROM order_records
		%s
		GROUP BY status
	`, userFilter)
	statusRows, err := r.db.QueryContext(ctx, statusQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询状态统计失败: %w", err)
	}
	defer statusRows.Close()

	for statusRows.Next() {
		var status string
		var count int64
		if err := statusRows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("扫描状态统计失败: %w", err)
		}
		stats.StatusCounts[status] = count
	}

	return stats, nil
}

// extractAddressSummary 提取地址摘要信息
func extractAddressSummary(addressJSON string) string {
	if addressJSON == "" {
		return ""
	}

	// 尝试解析JSON
	var addressInfo map[string]interface{}
	if err := json.Unmarshal([]byte(addressJSON), &addressInfo); err != nil {
		return "地址信息"
	}

	// 提取关键信息
	var parts []string
	if name, ok := addressInfo["name"].(string); ok && name != "" {
		parts = append(parts, name)
	}
	if province, ok := addressInfo["province"].(string); ok && province != "" {
		parts = append(parts, province)
	}
	if city, ok := addressInfo["city"].(string); ok && city != "" {
		parts = append(parts, city)
	}

	if len(parts) > 0 {
		return strings.Join(parts, " ")
	}
	return "地址信息"
}

// getExpressName 获取快递公司名称
func getExpressName(expressType string) string {
	// 这里可以从配置中获取快递公司名称
	// 简化实现，直接返回类型
	expressNames := map[string]string{
		"SF":   "顺丰速运",
		"YTO":  "圆通速递",
		"ZTO":  "中通快递",
		"STO":  "申通快递",
		"YD":   "韵达速递",
		"HTKY": "百世快递",
		"JD":   "京东物流",
		"EMS":  "中国邮政",
		"YZPY": "邮政包裹",
		"DBL":  "德邦快递",
	}

	if name, ok := expressNames[expressType]; ok {
		return name
	}
	return expressType
}

// FindByCustomerOrderNoAndProvider 根据客户订单号和供应商查询订单记录
func (r *PostgresOrderRepository) FindByCustomerOrderNoAndProvider(ctx context.Context, customerOrderNo, provider string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at,
			COALESCE(task_id, '') as task_id, COALESCE(poll_token, '') as poll_token,
			COALESCE(user_id, '') as user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight, COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code
		FROM order_records
		WHERE customer_order_no = $1 AND provider = $2
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, customerOrderNo, provider).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("订单记录不存在")
		}
		return nil, fmt.Errorf("查询订单记录失败: %w", err)
	}

	return &order, nil
}

// UpdatePriceValidation 更新订单的价格验证结果
func (r *PostgresOrderRepository) UpdatePriceValidation(ctx context.Context, orderID int64, validation *model.PriceValidationResult) error {
	query := `
		UPDATE order_records SET
			price_validation_provider_price = $2,
			price_validation_system_price = $3,
			price_validation_profit_status = $4,
			price_validation_query_status = $5,
			price_validation_query_time = $6,
			price_validation_error_message = $7,
			price_validation_supported = $8,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`

	var queryTime *time.Time
	if validation.QueryTime != "" {
		if t, err := time.Parse(time.RFC3339, validation.QueryTime); err == nil {
			queryTime = &t
		}
	}

	_, err := r.db.ExecContext(ctx, query,
		orderID,
		validation.ProviderPrice,
		validation.SystemPrice,
		validation.ProfitStatus,
		validation.QueryStatus,
		queryTime,
		validation.ErrorMessage,
		validation.Supported,
	)

	if err != nil {
		return fmt.Errorf("更新价格验证结果失败: %w", err)
	}

	return nil
}

// BatchUpdatePriceValidation 批量更新价格验证结果
func (r *PostgresOrderRepository) BatchUpdatePriceValidation(ctx context.Context, results []*model.PriceValidationResult) error {
	if len(results) == 0 {
		return nil
	}

	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	stmt, err := tx.PrepareContext(ctx, `
		UPDATE order_records SET
			price_validation_provider_price = $2,
			price_validation_system_price = $3,
			price_validation_profit_status = $4,
			price_validation_query_status = $5,
			price_validation_query_time = $6,
			price_validation_error_message = $7,
			price_validation_supported = $8,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`)
	if err != nil {
		return fmt.Errorf("准备语句失败: %w", err)
	}
	defer stmt.Close()

	for _, result := range results {
		var queryTime *time.Time
		if result.QueryTime != "" {
			if t, err := time.Parse(time.RFC3339, result.QueryTime); err == nil {
				queryTime = &t
			}
		}

		_, err = stmt.ExecContext(ctx,
			result.OrderID,
			result.ProviderPrice,
			result.SystemPrice,
			result.ProfitStatus,
			result.QueryStatus,
			queryTime,
			result.ErrorMessage,
			result.Supported,
		)
		if err != nil {
			return fmt.Errorf("更新订单 %d 价格验证结果失败: %w", result.OrderID, err)
		}
	}

	if err = tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	return nil
}

// UpdateStatus 更新订单状态
func (r *PostgresOrderRepository) UpdateStatus(ctx context.Context, orderID int64, newStatus string) error {
	query := `
		UPDATE order_records
		SET status = $2, updated_at = CURRENT_TIMESTAMP
		WHERE id = $1
	`

	result, err := r.db.ExecContext(ctx, query, orderID, newStatus)
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在或状态未发生变化")
	}

	return nil
}

// FindByOrderNo 根据订单号查询订单记录
func (r *PostgresOrderRepository) FindByOrderNo(ctx context.Context, orderNo string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at,
			COALESCE(task_id, '') as task_id, COALESCE(poll_token, '') as poll_token,
			COALESCE(user_id, '') as user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight, COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			-- 🔥 修复：添加失败订单相关字段
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_source, '') as failure_source,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry
		FROM order_records
		WHERE order_no = $1
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, orderNo).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
		// 🔥 修复：添加失败订单相关字段的扫描（包含缺失的failure_source字段）
		&order.FailureReason, &order.FailureMessage, &order.FailureStage,
		&order.FailureSource, &order.FailureTime, &order.CanRetry,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("订单记录不存在")
		}
		return nil, fmt.Errorf("查询订单记录失败: %w", err)
	}

	return &order, nil
}

// FindByPlatformOrderNoAndProvider 根据平台订单号和供应商查询订单记录（用于快递100等特殊情况）
func (r *PostgresOrderRepository) FindByPlatformOrderNoAndProvider(ctx context.Context, platformOrderNo, provider string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at,
			COALESCE(task_id, '') as task_id, COALESCE(poll_token, '') as poll_token,
			COALESCE(user_id, '') as user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight, COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			-- 🔥 修复：添加失败订单相关字段
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_source, '') as failure_source,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry
		FROM order_records
		WHERE platform_order_no = $1 AND provider = $2
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, platformOrderNo, provider).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
		// 🔥 修复：添加失败订单相关字段的扫描（包含缺失的failure_source字段）
		&order.FailureReason, &order.FailureMessage, &order.FailureStage,
		&order.FailureSource, &order.FailureTime, &order.CanRetry,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("订单记录不存在")
		}
		return nil, fmt.Errorf("查询订单记录失败: %w", err)
	}

	return &order, nil
}

// UpdateOrderStatus 更新订单状态（智能状态更新，防止状态倒退）
func (r *PostgresOrderRepository) UpdateOrderStatus(ctx context.Context, req *OrderStatusUpdateRequest) error {
	// 🔥 企业级修复：智能状态更新逻辑，防止回调顺序导致的状态覆盖

	// 1. 获取当前订单信息（包括用户ID和客户订单号）
	var currentStatus string
	var currentUpdatedAt time.Time
	var userID, customerOrderNo string
	getCurrentQuery := `
		SELECT status, updated_at, user_id, customer_order_no
		FROM order_records
		WHERE order_no = $1
	`

	err := r.db.QueryRowContext(ctx, getCurrentQuery, req.OrderNo).Scan(&currentStatus, &currentUpdatedAt, &userID, &customerOrderNo)
	if err != nil {
		if err == sql.ErrNoRows {
			return fmt.Errorf("订单不存在: %s", req.OrderNo)
		}
		return fmt.Errorf("获取当前订单状态失败: %w", err)
	}

	// 2. 检查是否需要更新状态
	shouldUpdate, reason := r.shouldUpdateOrderStatus(currentStatus, req.NewStatus, currentUpdatedAt, req.UpdateTime)
	if !shouldUpdate {
		// 记录跳过更新的原因，但不返回错误
		r.logger.Info("跳过订单状态更新",
			zap.String("order_no", req.OrderNo),
			zap.String("current_status", currentStatus),
			zap.String("attempted_status", req.NewStatus),
			zap.String("reason", reason),
			zap.String("provider", req.Provider))
		return nil
	}

	// 3. 在事务中执行状态更新和历史记录
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("开始事务失败: %w", err)
	}
	defer tx.Rollback()

	// 4. 执行状态更新
	updateQuery := `
		UPDATE order_records SET
			status = $1,
			updated_at = $2
		WHERE order_no = $3
	`

	result, err := tx.ExecContext(ctx, updateQuery, req.NewStatus, util.NowBeijing(), req.OrderNo)
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在或状态未变更")
	}

	// 5. 记录状态变更历史（企业级完整记录）
	extraBytes, err := json.Marshal(req.Extra)
	if err != nil {
		extraBytes = []byte("{}")
	}

	historyQuery := `
		INSERT INTO order_status_history (
			order_no, from_status, to_status, provider, raw_status,
			change_source, operator_id, operator_name, change_reason,
			user_id, customer_order_no, extra, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
		)`

	now := util.NowBeijing()
	changeSource := "callback" // 默认为回调来源
	if req.Extra != nil {
		if source, ok := req.Extra["change_source"].(string); ok && source != "" {
			changeSource = source
		}
	}

	var operatorID, operatorName, changeReason string
	if req.Extra != nil {
		if id, ok := req.Extra["operator_id"].(string); ok {
			operatorID = id
		}
		if name, ok := req.Extra["operator_name"].(string); ok {
			operatorName = name
		}
		if reason, ok := req.Extra["change_reason"].(string); ok {
			changeReason = reason
		}
	}

	_, err = tx.ExecContext(ctx, historyQuery,
		req.OrderNo,
		currentStatus,
		req.NewStatus,
		req.Provider,
		"", // raw_status 可以从 extra 中获取
		changeSource,
		operatorID,
		operatorName,
		changeReason,
		userID,
		customerOrderNo,
		extraBytes,
		now,
		now,
	)
	if err != nil {
		return fmt.Errorf("记录状态变更历史失败: %w", err)
	}

	// 6. 提交事务
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	// 7. 记录状态更新成功
	r.logger.Info("订单状态更新成功",
		zap.String("order_no", req.OrderNo),
		zap.String("from_status", currentStatus),
		zap.String("to_status", req.NewStatus),
		zap.String("provider", req.Provider),
		zap.String("change_source", changeSource),
		zap.Time("update_time", req.UpdateTime))

	return nil
}

// UpdateOrderBilling 更新订单计费信息
func (r *PostgresOrderRepository) UpdateOrderBilling(ctx context.Context, req *OrderBillingUpdateRequest) error {
	// 首先获取当前订单信息，确保不会覆盖重要字段
	currentOrder, err := r.FindByOrderNo(ctx, req.OrderNo)
	if err != nil {
		return fmt.Errorf("获取当前订单信息失败: %w", err)
	}

	// 确定要更新的字段值
	updateWeight := req.Weight
	updatePrice := currentOrder.Price // 保护Price不被回调重置

	// 只有在Price为0且有有效费用时才更新（修复历史数据）
	if req.TotalFee > 0 && currentOrder.Price == 0 {
		updatePrice = req.TotalFee
	}

	query := `
		UPDATE order_records SET
			weight = $1,
			price = $2,
			actual_fee = $3,
			actual_weight = $4,
			charged_weight = $5,
			updated_at = $6
		WHERE order_no = $7
	`

	result, err := r.db.ExecContext(ctx, query,
		updateWeight,
		updatePrice,
		req.TotalFee,      // actual_fee 使用传入的费用
		req.Weight,        // actual_weight 使用传入的重量
		req.ChargedWeight, // charged_weight 使用传入的计费重量
		util.NowBeijing(),
		req.OrderNo)
	if err != nil {
		return fmt.Errorf("更新订单计费信息失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在或计费信息未变更")
	}

	// 这里可以添加更详细的计费信息记录到专门的计费表
	// 例如：保存费用明细、重量信息、体积信息等
	// 如果需要保存费用明细，可以序列化并存储到专门的字段或表中

	return nil
}

// UpdateOrderActualFeeOnly 只更新订单的actual_fee字段，避免覆盖其他字段
func (r *PostgresOrderRepository) UpdateOrderActualFeeOnly(ctx context.Context, req *OrderBillingUpdateRequest) error {
	query := `
		UPDATE order_records SET
			actual_fee = $1,
			updated_at = $2
		WHERE order_no = $3
	`

	result, err := r.db.ExecContext(ctx, query,
		req.TotalFee, // actual_fee 使用传入的费用
		util.NowBeijing(),
		req.OrderNo)
	if err != nil {
		return fmt.Errorf("更新订单actual_fee失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在或actual_fee未变更")
	}

	return nil
}

// UpdateOrderPickupInfo 只更新订单的揽收信息字段，避免覆盖其他字段
func (r *PostgresOrderRepository) UpdateOrderPickupInfo(ctx context.Context, req *OrderPickupUpdateRequest) error {
	// 🔥 企业级精准更新：只更新揽收相关字段
	query := `
		UPDATE order_records SET
			courier_name = CASE WHEN $2 != '' THEN $2 ELSE courier_name END,
			courier_phone = CASE WHEN $3 != '' THEN $3 ELSE courier_phone END,
			courier_code = CASE WHEN $4 != '' THEN $4 ELSE courier_code END,
			station_name = CASE WHEN $5 != '' THEN $5 ELSE station_name END,
			pickup_code = CASE WHEN $6 != '' THEN $6 ELSE pickup_code END,
			updated_at = $7
		WHERE order_no = $1
	`

	result, err := r.db.ExecContext(ctx, query,
		req.OrderNo,
		req.CourierName,
		req.CourierPhone,
		req.CourierCode,
		req.StationName,
		req.PickupCode,
		util.NowBeijing())

	if err != nil {
		return fmt.Errorf("更新订单揽收信息失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在或揽收信息未变更")
	}

	return nil
}

// UpdateOrderTrackingNo 只更新订单的运单号字段，避免覆盖其他字段
func (r *PostgresOrderRepository) UpdateOrderTrackingNo(ctx context.Context, orderNo, trackingNo string) error {
	// 🔥 企业级精准更新：只更新运单号字段，不影响其他字段
	query := `
		UPDATE order_records SET
			tracking_no = $2,
			updated_at = $3
		WHERE order_no = $1 AND (tracking_no IS NULL OR tracking_no = '' OR tracking_no != $2)
	`

	result, err := r.db.ExecContext(ctx, query, orderNo, trackingNo, util.NowBeijing())
	if err != nil {
		return fmt.Errorf("更新订单运单号失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected > 0 {
		r.logger.Info("订单运单号更新成功",
			zap.String("order_no", orderNo),
			zap.String("tracking_no", trackingNo))
	} else {
		r.logger.Debug("订单运单号无需更新",
			zap.String("order_no", orderNo),
			zap.String("tracking_no", trackingNo))
	}

	return nil
}

// UpdateOrderWeightInfo 只更新订单的重量体积信息字段，避免覆盖其他字段
func (r *PostgresOrderRepository) UpdateOrderWeightInfo(ctx context.Context, req *OrderWeightUpdateRequest) error {
	// 🔥 企业级精准更新：只更新重量体积相关字段
	query := `
		UPDATE order_records SET
			actual_weight = CASE WHEN $2 > 0 THEN $2 ELSE actual_weight END,
			actual_volume = CASE WHEN $3 > 0 THEN $3 ELSE actual_volume END,
			charged_weight = CASE WHEN $4 > 0 THEN $4 ELSE charged_weight END,
			overweight_fee = CASE WHEN $5 >= 0 THEN $5 ELSE overweight_fee END,
			underweight_fee = CASE WHEN $6 >= 0 THEN $6 ELSE underweight_fee END,
			updated_at = $7
		WHERE order_no = $1
	`

	result, err := r.db.ExecContext(ctx, query,
		req.OrderNo,
		req.ActualWeight,
		req.ActualVolume,
		req.ChargedWeight,
		req.OverweightFee,
		req.UnderweightFee,
		util.NowBeijing())

	if err != nil {
		return fmt.Errorf("更新订单重量信息失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("订单不存在或重量信息未变更")
	}

	return nil
}

// ===== 管理员专用方法实现 =====

// AdminListWithFilter 管理员订单列表查询（跨用户）
func (r *PostgresOrderRepository) AdminListWithFilter(ctx context.Context, req *model.AdminOrderListRequest) ([]*model.AdminOrderListItem, int64, error) {
	// 构建WHERE条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	// 用户过滤条件
	if req.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("o.user_id = $%d", argIndex))
		args = append(args, req.UserID)
		argIndex++
	}

	if req.Username != "" {
		conditions = append(conditions, fmt.Sprintf("u.username ILIKE $%d", argIndex))
		args = append(args, "%"+req.Username+"%")
		argIndex++
	}

	if req.UserEmail != "" {
		conditions = append(conditions, fmt.Sprintf("u.email ILIKE $%d", argIndex))
		args = append(args, "%"+req.UserEmail+"%")
		argIndex++
	}

	// 订单状态过滤
	if req.Status != "" {
		conditions = append(conditions, fmt.Sprintf("o.status = $%d", argIndex))
		args = append(args, req.Status)
		argIndex++
	}

	// 快递公司过滤
	if req.ExpressType != "" {
		conditions = append(conditions, fmt.Sprintf("o.express_type = $%d", argIndex))
		args = append(args, req.ExpressType)
		argIndex++
	}

	if req.CompanyCode != "" {
		conditions = append(conditions, fmt.Sprintf("ec.code = $%d", argIndex))
		args = append(args, req.CompanyCode)
		argIndex++
	}

	// 供应商过滤
	if req.Provider != "" {
		conditions = append(conditions, fmt.Sprintf("o.provider = $%d", argIndex))
		args = append(args, req.Provider)
		argIndex++
	}

	// 计费状态过滤
	if req.BillingStatus != "" {
		conditions = append(conditions, fmt.Sprintf("COALESCE(o.billing_status, 'pending') = $%d", argIndex))
		args = append(args, req.BillingStatus)
		argIndex++
	}

	// 价格范围过滤
	if req.PriceMin != nil {
		conditions = append(conditions, fmt.Sprintf("o.price >= $%d", argIndex))
		args = append(args, *req.PriceMin)
		argIndex++
	}

	if req.PriceMax != nil {
		conditions = append(conditions, fmt.Sprintf("o.price <= $%d", argIndex))
		args = append(args, *req.PriceMax)
		argIndex++
	}

	// 重量范围过滤
	if req.WeightMin != nil {
		conditions = append(conditions, fmt.Sprintf("o.weight >= $%d", argIndex))
		args = append(args, *req.WeightMin)
		argIndex++
	}

	if req.WeightMax != nil {
		conditions = append(conditions, fmt.Sprintf("o.weight <= $%d", argIndex))
		args = append(args, *req.WeightMax)
		argIndex++
	}

	// 时间范围过滤
	if req.StartTime != "" {
		conditions = append(conditions, fmt.Sprintf("o.created_at >= $%d", argIndex))
		args = append(args, req.StartTime)
		argIndex++
	}

	if req.EndTime != "" {
		conditions = append(conditions, fmt.Sprintf("o.created_at <= $%d", argIndex))
		args = append(args, req.EndTime)
		argIndex++
	}

	if req.CreatedStartTime != "" {
		conditions = append(conditions, fmt.Sprintf("o.created_at >= $%d", argIndex))
		args = append(args, req.CreatedStartTime)
		argIndex++
	}

	if req.CreatedEndTime != "" {
		conditions = append(conditions, fmt.Sprintf("o.created_at <= $%d", argIndex))
		args = append(args, req.CreatedEndTime)
		argIndex++
	}

	if req.UpdatedStartTime != "" {
		conditions = append(conditions, fmt.Sprintf("o.updated_at >= $%d", argIndex))
		args = append(args, req.UpdatedStartTime)
		argIndex++
	}

	if req.UpdatedEndTime != "" {
		conditions = append(conditions, fmt.Sprintf("o.updated_at <= $%d", argIndex))
		args = append(args, req.UpdatedEndTime)
		argIndex++
	}

	// 关键词搜索
	if req.SearchKeyword != "" {
		searchFields := req.SearchFields
		if len(searchFields) == 0 {
			// 默认搜索字段
			searchFields = []string{"customer_order_no", "order_no", "tracking_no"}
		}

		var searchConditions []string
		for _, field := range searchFields {
			switch field {
			case "customer_order_no":
				searchConditions = append(searchConditions, fmt.Sprintf("o.customer_order_no ILIKE $%d", argIndex))
			case "order_no":
				searchConditions = append(searchConditions, fmt.Sprintf("o.order_no ILIKE $%d", argIndex))
			case "tracking_no":
				searchConditions = append(searchConditions, fmt.Sprintf("o.tracking_no ILIKE $%d", argIndex))
			case "sender_info":
				searchConditions = append(searchConditions, fmt.Sprintf("o.sender_info::text ILIKE $%d", argIndex))
			case "receiver_info":
				searchConditions = append(searchConditions, fmt.Sprintf("o.receiver_info::text ILIKE $%d", argIndex))
			case "username":
				searchConditions = append(searchConditions, fmt.Sprintf("u.username ILIKE $%d", argIndex))
			case "user_email":
				searchConditions = append(searchConditions, fmt.Sprintf("u.email ILIKE $%d", argIndex))
			}
		}

		if len(searchConditions) > 0 {
			conditions = append(conditions, "("+strings.Join(searchConditions, " OR ")+")")
			args = append(args, "%"+req.SearchKeyword+"%")
			argIndex++
		}
	}

	// 构建WHERE子句
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 构建ORDER BY子句 - 使用白名单防止SQL注入
	sortBy := r.getSafeSortField(req.SortBy, "admin")
	if sortBy == "" {
		sortBy = "o.created_at"
	}
	sortOrder := strings.ToUpper(req.SortOrder)
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "DESC"
	}
	orderClause := fmt.Sprintf("ORDER BY %s %s", sortBy, sortOrder)

	// 计算偏移量
	offset := (req.Page - 1) * req.PageSize

	// 查询总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*)
		FROM order_records o
		LEFT JOIN users u ON o.user_id = u.id
		LEFT JOIN express_companies ec ON o.express_type = ec.code
		%s
	`, whereClause)

	var total int64
	err := r.db.QueryRowContext(ctx, countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("查询管理员订单总数失败: %w", err)
	}

	// 查询数据
	dataQuery := fmt.Sprintf(`
		SELECT
			o.id, COALESCE(o.platform_order_no, '') as platform_order_no, o.customer_order_no, o.order_no, o.tracking_no, o.express_type, o.product_type,
			o.provider, o.status, o.weight, o.price,
			o.sender_info, o.receiver_info, o.created_at, o.updated_at,
			COALESCE(o.actual_fee, 0) as actual_fee,
			COALESCE(o.insurance_fee, 0) as insurance_fee,
			COALESCE(o.billing_status, 'pending') as billing_status,
			COALESCE(o.order_volume, 0) as order_volume,
			COALESCE(o.actual_weight, 0) as actual_weight,
			COALESCE(o.actual_volume, 0) as actual_volume,
			COALESCE(o.charged_weight, 0) as charged_weight,
			COALESCE(o.courier_name, '') as courier_name,
			COALESCE(o.courier_phone, '') as courier_phone,
			COALESCE(o.courier_code, '') as courier_code,
			COALESCE(o.station_name, '') as station_name,
			COALESCE(o.pickup_code, '') as pickup_code,
			-- 🔥 企业级修复：添加缺失的task_id和poll_token字段
			COALESCE(o.task_id, '') as task_id,
			COALESCE(o.poll_token, '') as poll_token,
			-- 🔥 企业级修复：添加价格验证字段
			o.price_validation_provider_price,
			o.price_validation_system_price,
			o.price_validation_profit_status,
			o.price_validation_query_status,
			o.price_validation_query_time,
			o.price_validation_error_message,
			o.price_validation_supported,
			-- 用户信息
			u.id as user_id, u.username, u.email, u.is_active,
			-- 快递公司信息
			ec.id as company_id, ec.code as company_code, ec.name as company_name, ec.english_name as company_english_name
		FROM order_records o
		LEFT JOIN users u ON o.user_id = u.id
		LEFT JOIN express_companies ec ON o.express_type = ec.code
		%s
		%s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderClause, argIndex, argIndex+1)

	args = append(args, req.PageSize, offset)

	rows, err := r.db.QueryContext(ctx, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询管理员订单列表失败: %w", err)
	}
	defer rows.Close()

	var items []*model.AdminOrderListItem
	for rows.Next() {
		var item model.AdminOrderListItem
		var senderInfo, receiverInfo string
		var userID, username, email sql.NullString
		var userIsActive sql.NullBool
		var companyID, companyCode, companyName, companyEnglishName sql.NullString
		var productType sql.NullString // 处理可能为NULL的product_type字段
		var taskID, pollToken string   // 🔥 企业级修复：添加task_id和poll_token字段
		// 🔥 企业级修复：添加价格验证字段变量
		var priceValidationProviderPrice, priceValidationSystemPrice sql.NullFloat64
		var priceValidationProfitStatus, priceValidationQueryStatus, priceValidationErrorMessage sql.NullString
		var priceValidationQueryTime sql.NullTime
		var priceValidationSupported sql.NullBool

		err := rows.Scan(
			&item.ID, &item.PlatformOrderNo, &item.CustomerOrderNo, &item.ProviderOrderNo, &item.TrackingNo,
			&item.ExpressType, &productType, &item.Provider, &item.Status,
			&item.Weight, &item.Price, &senderInfo, &receiverInfo,
			&item.CreatedAt, &item.UpdatedAt,
			&item.Billing.ActualFee, &item.Billing.InsuranceFee, &item.Billing.BillingStatus,
			&item.WeightVolume.OrderVolume, &item.WeightVolume.ActualWeight,
			&item.WeightVolume.ActualVolume, &item.WeightVolume.ChargedWeight,
			&item.CourierName, &item.CourierPhone, &item.CourierCode,
			&item.StationName, &item.PickupCode,
			&taskID, &pollToken, // 🔥 企业级修复：扫描新增字段
			// 🔥 企业级修复：扫描价格验证字段
			&priceValidationProviderPrice, &priceValidationSystemPrice,
			&priceValidationProfitStatus, &priceValidationQueryStatus,
			&priceValidationQueryTime, &priceValidationErrorMessage, &priceValidationSupported,
			&userID, &username, &email, &userIsActive,
			&companyID, &companyCode, &companyName, &companyEnglishName,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("扫描管理员订单记录失败: %w", err)
		}

		// 🔥 设置兼容性字段：OrderNo优先显示平台订单号
		if item.PlatformOrderNo != "" {
			item.OrderNo = item.PlatformOrderNo
		} else {
			item.OrderNo = item.ProviderOrderNo
		}

		// 处理可能为NULL的字段
		if productType.Valid {
			item.ProductType = productType.String
		}

		// 🔥 企业级修复：设置task_id和poll_token字段
		item.TaskId = taskID
		item.PollToken = pollToken

		// 处理寄件人和收件人信息摘要
		item.SenderInfo = extractAddressSummary(senderInfo)
		item.ReceiverInfo = extractAddressSummary(receiverInfo)

		// 填充用户信息
		if userID.Valid {
			item.User.ID = userID.String
			item.User.Username = username.String
			item.User.Email = email.String
			item.User.IsActive = userIsActive.Bool
		}

		// 填充快递公司信息
		if companyCode.Valid {
			item.ExpressCompany.Code = companyCode.String
			item.ExpressCompany.Name = companyName.String
			item.ExpressCompany.EnglishName = companyEnglishName.String
		}

		// 计算费用相关信息
		item.Billing.EstimatedFee = item.Price
		item.Billing.FeeDifference = item.Billing.ActualFee - item.Billing.EstimatedFee

		// 填充重量体积信息
		item.WeightVolume.OrderWeight = item.Weight

		// 🔥 企业级修复：填充价格验证信息（从数据库读取或初始化）
		if priceValidationProviderPrice.Valid {
			item.PriceValidation.ProviderPrice = priceValidationProviderPrice.Float64
		}
		if priceValidationSystemPrice.Valid {
			item.PriceValidation.SystemPrice = priceValidationSystemPrice.Float64
		} else {
			item.PriceValidation.SystemPrice = item.Price // 默认使用订单价格
		}
		if priceValidationProfitStatus.Valid {
			item.PriceValidation.ProfitStatus = priceValidationProfitStatus.String
		} else {
			item.PriceValidation.ProfitStatus = "unknown"
		}
		if priceValidationQueryStatus.Valid {
			item.PriceValidation.QueryStatus = priceValidationQueryStatus.String
		} else {
			item.PriceValidation.QueryStatus = "pending"
		}
		if priceValidationQueryTime.Valid {
			item.PriceValidation.QueryTime = util.ToBeijing(priceValidationQueryTime.Time).Format(time.RFC3339)
		}
		if priceValidationErrorMessage.Valid {
			item.PriceValidation.ErrorMessage = priceValidationErrorMessage.String
		}
		if priceValidationSupported.Valid {
			item.PriceValidation.Supported = priceValidationSupported.Bool
		} else {
			item.PriceValidation.Supported = false
		}

		// 设置操作权限
		permissions := model.GetAdminOrderOperationPermissions(item.Status)
		item.Operations.CanCancel = permissions["can_cancel"]
		item.Operations.CanUpdateStatus = permissions["can_update_status"]
		item.Operations.CanRefund = permissions["can_refund"]

		items = append(items, &item)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("迭代管理员订单记录失败: %w", err)
	}

	return items, total, nil
}

// BatchUpdateStatus 批量更新订单状态
func (r *PostgresOrderRepository) BatchUpdateStatus(ctx context.Context, orderIDs []int64, newStatus string) error {
	if len(orderIDs) == 0 {
		return fmt.Errorf("订单ID列表不能为空")
	}

	// 构建批量更新SQL
	placeholders := make([]string, len(orderIDs))
	args := make([]interface{}, len(orderIDs)+2)
	args[0] = newStatus
	args[1] = util.NowBeijing()

	for i, orderID := range orderIDs {
		placeholders[i] = fmt.Sprintf("$%d", i+3)
		args[i+2] = orderID
	}

	query := fmt.Sprintf(`
		UPDATE order_records
		SET status = $1, updated_at = $2
		WHERE id IN (%s)
	`, strings.Join(placeholders, ","))

	result, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("批量更新订单状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取受影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("没有订单被更新")
	}

	return nil
}

// GetAdminStatistics 获取管理员统计数据
func (r *PostgresOrderRepository) GetAdminStatistics(ctx context.Context, filter *AdminStatisticsFilter) (*model.AdminOrderStatistics, error) {
	stats := &model.AdminOrderStatistics{
		StatusCounts:   make(map[string]int64),
		ProviderCounts: make(map[string]int64),
		CompanyCounts:  make(map[string]int64),
		BillingCounts:  make(map[string]int64),
	}

	// 构建基础WHERE条件
	var conditions []string
	var args []interface{}
	argIndex := 1

	if filter.UserID != "" {
		conditions = append(conditions, fmt.Sprintf("user_id = $%d", argIndex))
		args = append(args, filter.UserID)
		argIndex++
	}

	if filter.Status != "" {
		conditions = append(conditions, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, filter.Status)
		argIndex++
	}

	if filter.Provider != "" {
		conditions = append(conditions, fmt.Sprintf("provider = $%d", argIndex))
		args = append(args, filter.Provider)
		argIndex++
	}

	if filter.BillingStatus != "" {
		conditions = append(conditions, fmt.Sprintf("COALESCE(billing_status, 'pending') = $%d", argIndex))
		args = append(args, filter.BillingStatus)
		argIndex++
	}

	if filter.StartTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, filter.StartTime)
		argIndex++
	}

	if filter.EndTime != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, filter.EndTime)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 基础统计查询
	basicStatsQuery := fmt.Sprintf(`
		SELECT
			COUNT(*) as total_orders,
			COALESCE(SUM(price), 0) as total_amount,
			COUNT(DISTINCT user_id) as active_users
		FROM order_records
		%s
	`, whereClause)

	err := r.db.QueryRowContext(ctx, basicStatsQuery, args...).Scan(
		&stats.TotalOrders,
		&stats.TotalAmount,
		&stats.ActiveUsers,
	)
	if err != nil {
		return nil, fmt.Errorf("查询基础统计失败: %w", err)
	}

	// 今日统计查询
	todayConditions := append(conditions, "DATE(created_at) = CURRENT_DATE")
	todayWhereClause := "WHERE " + strings.Join(todayConditions, " AND ")

	todayStatsQuery := fmt.Sprintf(`
		SELECT
			COUNT(*) as today_orders,
			COALESCE(SUM(price), 0) as today_amount
		FROM order_records
		%s
	`, todayWhereClause)

	err = r.db.QueryRowContext(ctx, todayStatsQuery, args...).Scan(
		&stats.TodayOrders,
		&stats.TodayAmount,
	)
	if err != nil {
		return nil, fmt.Errorf("查询今日统计失败: %w", err)
	}

	// 状态统计
	statusStatsQuery := fmt.Sprintf(`
		SELECT status, COUNT(*)
		FROM order_records
		%s
		GROUP BY status
	`, whereClause)

	rows, err := r.db.QueryContext(ctx, statusStatsQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询状态统计失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var status string
		var count int64
		if err := rows.Scan(&status, &count); err != nil {
			return nil, fmt.Errorf("扫描状态统计失败: %w", err)
		}
		stats.StatusCounts[status] = count
	}

	// 供应商统计
	providerStatsQuery := fmt.Sprintf(`
		SELECT provider, COUNT(*)
		FROM order_records
		%s
		GROUP BY provider
	`, whereClause)

	rows, err = r.db.QueryContext(ctx, providerStatsQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询供应商统计失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var provider string
		var count int64
		if err := rows.Scan(&provider, &count); err != nil {
			return nil, fmt.Errorf("扫描供应商统计失败: %w", err)
		}
		stats.ProviderCounts[provider] = count
	}

	// 快递公司统计
	companyStatsQuery := fmt.Sprintf(`
		SELECT express_type, COUNT(*)
		FROM order_records
		%s
		GROUP BY express_type
	`, whereClause)

	rows, err = r.db.QueryContext(ctx, companyStatsQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询快递公司统计失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var expressType string
		var count int64
		if err := rows.Scan(&expressType, &count); err != nil {
			return nil, fmt.Errorf("扫描快递公司统计失败: %w", err)
		}
		stats.CompanyCounts[expressType] = count
	}

	// 计费状态统计
	billingStatsQuery := fmt.Sprintf(`
		SELECT COALESCE(billing_status, 'pending'), COUNT(*)
		FROM order_records
		%s
		GROUP BY COALESCE(billing_status, 'pending')
	`, whereClause)

	rows, err = r.db.QueryContext(ctx, billingStatsQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("查询计费状态统计失败: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var billingStatus string
		var count int64
		if err := rows.Scan(&billingStatus, &count); err != nil {
			return nil, fmt.Errorf("扫描计费状态统计失败: %w", err)
		}
		stats.BillingCounts[billingStatus] = count
	}

	// 异常订单统计
	exceptionStatsQuery := fmt.Sprintf(`
		SELECT
			COUNT(CASE WHEN status = 'exception' THEN 1 END) as exception_orders,
			COUNT(CASE WHEN status = 'cancelled' OR status = 'refunded' THEN 1 END) as refund_orders
		FROM order_records
		%s
	`, whereClause)

	err = r.db.QueryRowContext(ctx, exceptionStatsQuery, args...).Scan(
		&stats.ExceptionOrders,
		&stats.RefundOrders,
	)
	if err != nil {
		return nil, fmt.Errorf("查询异常统计失败: %w", err)
	}

	// 设置统计时间范围
	stats.StatisticsTime.StartTime = util.NowBeijing().AddDate(0, 0, -30) // 默认30天
	stats.StatisticsTime.EndTime = util.NowBeijing()

	if filter.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02 15:04:05", filter.StartTime); err == nil {
			stats.StatisticsTime.StartTime = startTime
		}
	}

	if filter.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02 15:04:05", filter.EndTime); err == nil {
			stats.StatisticsTime.EndTime = endTime
		}
	}

	return stats, nil
}

// getSafeSortField 获取安全的排序字段，防止SQL注入
func (r *PostgresOrderRepository) getSafeSortField(field, userType string) string {
	// 定义安全的排序字段白名单
	adminSortFields := map[string]string{
		"created_at":   "o.created_at",
		"updated_at":   "o.updated_at",
		"price":        "o.price",
		"weight":       "o.weight",
		"user_id":      "o.user_id",
		"status":       "o.status",
		"provider":     "o.provider",
		"express_type": "o.express_type",
		"username":     "u.username",
		"user_email":   "u.email",
		"order_no":     "o.order_no",
		"tracking_no":  "o.tracking_no",
	}

	userSortFields := map[string]string{
		"created_at":  "o.created_at",
		"updated_at":  "o.updated_at",
		"price":       "o.price",
		"weight":      "o.weight",
		"status":      "o.status",
		"order_no":    "o.order_no",
		"tracking_no": "o.tracking_no",
	}

	var validFields map[string]string
	if userType == "admin" {
		validFields = adminSortFields
	} else {
		validFields = userSortFields
	}

	if safeField, exists := validFields[field]; exists {
		return safeField
	}

	// 如果字段不在白名单中，返回空字符串
	return ""
}

// GenerateCustomerOrderNo 生成客户订单号
func (r *PostgresOrderRepository) GenerateCustomerOrderNo(ctx context.Context, provider string) (string, error) {
	// 供应商前缀映射
	prefixMap := map[string]string{
		"yida":      "yd",
		"yuntong":   "yt",
		"kuaidi100": "kd",
	}

	prefix, exists := prefixMap[provider]
	if !exists {
		// 未知供应商使用通用前缀
		prefix = "gk"
	}

	// 创建或获取该供应商的序列
	seqName := fmt.Sprintf("customer_order_seq_%s", provider)

	// 确保序列存在
	createSeqSQL := fmt.Sprintf(`
		DO $$
		BEGIN
			IF NOT EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = '%s') THEN
				CREATE SEQUENCE %s START 1000001 INCREMENT 1;
			END IF;
		END $$;
	`, seqName, seqName)

	_, err := r.db.ExecContext(ctx, createSeqSQL)
	if err != nil {
		return "", fmt.Errorf("创建序列失败: %w", err)
	}

	// 获取下一个序列值
	var nextVal int64
	err = r.db.QueryRowContext(ctx, fmt.Sprintf("SELECT nextval('%s')", seqName)).Scan(&nextVal)
	if err != nil {
		return "", fmt.Errorf("获取序列值失败: %w", err)
	}

	// 生成订单号：前缀 + 7位数字
	customerOrderNo := fmt.Sprintf("%s%07d", prefix, nextVal)

	return customerOrderNo, nil
}

// 🔥 ExecRawSQL 执行原生SQL（用于幂等性检查等特殊需求）
func (r *PostgresOrderRepository) ExecRawSQL(ctx context.Context, query string, args ...interface{}) error {
	_, err := r.db.ExecContext(ctx, query, args...)
	return err
}

// 🔥 QueryRawSQL 执行原生SQL查询（用于幂等性检查等特殊需求）
func (r *PostgresOrderRepository) QueryRawSQL(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return r.db.QueryContext(ctx, query, args...)
}

// 🔥 QueryRawSQLCount 执行原生SQL查询并返回计数
func (r *PostgresOrderRepository) QueryRawSQLCount(ctx context.Context, query string, args ...interface{}) (int64, error) {
	var count int64
	err := r.db.QueryRowContext(ctx, query, args...).Scan(&count)
	return count, err
}

// 🔥 QueryRawSQLOrders 执行原生SQL查询并返回订单列表
func (r *PostgresOrderRepository) QueryRawSQLOrders(ctx context.Context, query string, args ...interface{}) ([]*model.OrderRecord, error) {
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var orders []*model.OrderRecord
	for rows.Next() {
		var order model.OrderRecord
		err := rows.Scan(
			&order.ID, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
			&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
			&order.Weight, &order.Price, &order.UserID, &order.CreatedAt, &order.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		orders = append(orders, &order)
	}

	return orders, rows.Err()
}

// shouldUpdateOrderStatus 智能判断是否应该更新订单状态
// 🔥 企业级状态更新逻辑：防止回调顺序导致的状态倒退
func (r *PostgresOrderRepository) shouldUpdateOrderStatus(currentStatus, newStatus string, currentUpdatedAt, newUpdateTime time.Time) (bool, string) {
	// 1. 如果状态相同，不需要更新
	if currentStatus == newStatus {
		return false, "状态相同"
	}

	// 2. 定义状态优先级（数字越大优先级越高）
	statusPriority := map[string]int{
		"created":            0, // 已创建（初始状态）
		"submitted":          1, // 已提交
		"assigned":           2, // 已分配
		"awaiting_pickup":    3, // 等待揽收
		"picked_up":          4, // 已揽收
		"in_transit":         5, // 运输中
		"out_for_delivery":   6, // 派送中
		"delivered":          7, // 已签收
		"delivered_abnormal": 7, // 异常签收
		"cancelling":         7, // 取消中（与已签收同级，允许最终状态覆盖）
		"cancelled":          8, // 已取消（最高优先级，一旦取消不可逆转）
		"voided":             8, // 已作废
		"exception":          3, // 异常（与等待揽收同级）
		"returned":           6, // 退回（与派送中同级）
		"billed":             5, // 已计费（与运输中同级）
	}

	currentPriority, currentExists := statusPriority[currentStatus]
	newPriority, newExists := statusPriority[newStatus]

	// 3. 如果状态不在优先级表中，使用时间判断
	if !currentExists || !newExists {
		// 对于未知状态，如果新状态的时间更新，则允许更新
		if newUpdateTime.After(currentUpdatedAt) {
			return true, "未知状态，基于时间判断"
		}
		return false, "未知状态，时间较早"
	}

	// 4. 特殊规则：已取消/已作废状态不可被其他状态覆盖
	if currentStatus == "cancelled" || currentStatus == "voided" {
		if newStatus == "cancelled" || newStatus == "voided" {
			return true, "取消状态更新"
		}
		return false, "订单已取消/作废，拒绝状态变更"
	}

	// 5. 优先级判断
	if newPriority > currentPriority {
		// 新状态优先级更高，允许更新
		return true, "状态优先级提升"
	} else if newPriority < currentPriority {
		// 新状态优先级更低，检查时间差
		timeDiff := newUpdateTime.Sub(currentUpdatedAt)

		// 如果时间差超过5分钟，可能是延迟的回调，拒绝更新
		if timeDiff < -5*time.Minute {
			return false, "状态倒退且时间过早，可能是延迟回调"
		}

		// 如果时间差在合理范围内，记录警告但允许更新
		if timeDiff > -1*time.Minute {
			return true, "状态倒退但时间合理，允许更新"
		}

		return false, "状态倒退，拒绝更新"
	} else {
		// 优先级相同，基于时间判断
		if newUpdateTime.After(currentUpdatedAt) {
			return true, "同级状态，基于时间更新"
		}
		return false, "同级状态，时间较早"
	}
}

// ========== 失败订单相关方法实现 ==========

// CreateFailedOrder 创建失败订单记录
func (r *PostgresOrderRepository) CreateFailedOrder(ctx context.Context, order *model.OrderRecord) error {
	query := `
		INSERT INTO order_records (
			customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, actual_fee, insurance_fee,
			overweight_fee, underweight_fee, weight_adjustment_reason, billing_status,
			order_volume, actual_weight, actual_volume, charged_weight,
			sender_info, receiver_info, package_info, request_data, response_data,
			order_code, created_at, updated_at, task_id, poll_token, user_id,
			courier_name, courier_phone, courier_code, station_name, pickup_code,
			failure_reason, failure_message, failure_stage, failure_source, failure_time, can_retry
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
			$16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30,
			$31, $32, $33, $34, $35, $36, $37, $38, $39, $40, $41
		) RETURNING id
	`

	var id int64
	err := r.db.QueryRowContext(
		ctx, query,
		order.CustomerOrderNo,
		order.OrderNo,
		order.TrackingNo,
		order.ExpressType,
		order.ProductType,
		order.Provider,
		order.Status,
		order.Weight,
		order.Price,
		order.ActualFee,
		order.InsuranceFee,
		order.OverweightFee,
		order.UnderweightFee,
		order.WeightAdjustmentReason,
		order.BillingStatus,
		order.OrderVolume,
		order.ActualWeight,
		order.ActualVolume,
		order.ChargedWeight,
		order.SenderInfo,
		order.ReceiverInfo,
		order.PackageInfo,
		order.RequestData,
		order.ResponseData,
		order.OrderCode,
		util.NowBeijing(),
		util.NowBeijing(),
		order.TaskId,
		order.PollToken,
		order.UserID,
		order.CourierName,
		order.CourierPhone,
		order.CourierCode,
		order.StationName,
		order.PickupCode,
		order.FailureReason,
		order.FailureMessage,
		order.FailureStage,
		order.FailureSource,
		order.FailureTime,
		order.CanRetry,
	).Scan(&id)

	if err != nil {
		return fmt.Errorf("创建失败订单记录失败: %w", err)
	}

	order.ID = id
	return nil
}

// GetFailedOrdersByUser 获取用户的失败订单
func (r *PostgresOrderRepository) GetFailedOrdersByUser(ctx context.Context, userID string, limit, offset int) ([]*model.OrderRecord, error) {
	query := `
		SELECT
			id, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at,
			COALESCE(task_id, '') as task_id, COALESCE(poll_token, '') as poll_token,
			COALESCE(user_id, '') as user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight, COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_source, '') as failure_source,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry
		FROM order_records
		WHERE user_id = $1 AND status = 'failed'
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := r.db.QueryContext(ctx, query, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询失败订单失败: %w", err)
	}
	defer rows.Close()

	var orders []*model.OrderRecord
	for rows.Next() {
		var order model.OrderRecord
		err := rows.Scan(
			&order.ID, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
			&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
			&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
			&order.PackageInfo, &order.RequestData, &order.ResponseData,
			&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
			&order.ActualFee, &order.InsuranceFee,
			&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
			&order.BillingStatus,
			&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
			&order.ChargedWeight,
			&order.CourierName, &order.CourierPhone, &order.CourierCode,
			&order.StationName, &order.PickupCode,
			&order.FailureReason, &order.FailureMessage, &order.FailureStage,
			&order.FailureSource, &order.FailureTime, &order.CanRetry,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描失败订单数据失败: %w", err)
		}
		orders = append(orders, &order)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历失败订单数据失败: %w", err)
	}

	return orders, nil
}

// GetFailedOrderDetail 获取失败订单详情
func (r *PostgresOrderRepository) GetFailedOrderDetail(ctx context.Context, orderNo string, userID string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, created_at, updated_at,
			COALESCE(task_id, '') as task_id, COALESCE(poll_token, '') as poll_token,
			COALESCE(user_id, '') as user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight, COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_source, '') as failure_source,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry
		FROM order_records
		WHERE order_no = $1 AND user_id = $2 AND status = 'failed'
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, orderNo, userID).Scan(
		&order.ID, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
		&order.FailureReason, &order.FailureMessage, &order.FailureStage,
		&order.FailureSource, &order.FailureTime, &order.CanRetry,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("查询失败订单详情失败: %w", err)
	}

	return &order, nil
}

// GetFailureStatistics 获取失败订单统计
func (r *PostgresOrderRepository) GetFailureStatistics(ctx context.Context, userID string, startTime time.Time) (*model.FailureStatistics, error) {
	// 总订单数和失败订单数
	totalQuery := `
		SELECT 
			COUNT(*) as total,
			COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
		FROM order_records 
		WHERE user_id = $1 AND created_at >= $2
	`

	var totalOrders, failedOrders int64
	err := r.db.QueryRowContext(ctx, totalQuery, userID, startTime).Scan(&totalOrders, &failedOrders)
	if err != nil {
		return nil, fmt.Errorf("获取订单总数统计失败: %w", err)
	}

	// 计算失败率
	var failureRate float64
	if totalOrders > 0 {
		failureRate = float64(failedOrders) / float64(totalOrders) * 100
	}

	// 失败原因统计
	reasonQuery := `
		SELECT 
			COALESCE(failure_reason, 'unknown') as reason,
			COUNT(*) as count
		FROM order_records 
		WHERE user_id = $1 AND status = 'failed' AND created_at >= $2
		GROUP BY failure_reason
		ORDER BY count DESC
	`

	reasonRows, err := r.db.QueryContext(ctx, reasonQuery, userID, startTime)
	if err != nil {
		return nil, fmt.Errorf("获取失败原因统计失败: %w", err)
	}
	defer reasonRows.Close()

	var reasonStats []*model.FailureReasonStat
	for reasonRows.Next() {
		var stat model.FailureReasonStat
		err := reasonRows.Scan(&stat.Reason, &stat.Count)
		if err != nil {
			return nil, fmt.Errorf("扫描失败原因统计失败: %w", err)
		}

		// 计算百分比
		if failedOrders > 0 {
			stat.Percentage = float64(stat.Count) / float64(failedOrders) * 100
		}

		// 添加原因描述
		stat.Description = r.getFailureReasonDescription(stat.Reason)
		reasonStats = append(reasonStats, &stat)
	}

	// 供应商失败统计
	providerQuery := `
		SELECT 
			provider,
			COUNT(*) as failed_count,
			(SELECT COUNT(*) FROM order_records WHERE user_id = $1 AND provider = o.provider AND created_at >= $2) as total_count
		FROM order_records o
		WHERE user_id = $1 AND status = 'failed' AND created_at >= $2
		GROUP BY provider
		ORDER BY failed_count DESC
	`

	providerRows, err := r.db.QueryContext(ctx, providerQuery, userID, startTime)
	if err != nil {
		return nil, fmt.Errorf("获取供应商失败统计失败: %w", err)
	}
	defer providerRows.Close()

	var providerStats []*model.FailureProviderStat
	for providerRows.Next() {
		var stat model.FailureProviderStat
		var totalCount int64
		err := providerRows.Scan(&stat.Provider, &stat.Count, &totalCount)
		if err != nil {
			return nil, fmt.Errorf("扫描供应商失败统计失败: %w", err)
		}

		// 计算百分比和失败率
		if failedOrders > 0 {
			stat.Percentage = float64(stat.Count) / float64(failedOrders) * 100
		}
		if totalCount > 0 {
			stat.FailureRate = float64(stat.Count) / float64(totalCount) * 100
		}

		providerStats = append(providerStats, &stat)
	}

	return &model.FailureStatistics{
		TotalOrders:   totalOrders,
		FailedOrders:  failedOrders,
		FailureRate:   failureRate,
		ReasonStats:   reasonStats,
		ProviderStats: providerStats,
		TimeWindow:    fmt.Sprintf("%s 至今", startTime.Format("2006-01-02 15:04:05")),
		LastUpdated:   time.Now(),
	}, nil
}

// DeleteExpiredFailedOrders 删除过期的失败订单
func (r *PostgresOrderRepository) DeleteExpiredFailedOrders(ctx context.Context, cutoffTime time.Time) (int, error) {
	query := `
		DELETE FROM order_records 
		WHERE status = 'failed' AND created_at < $1
	`

	result, err := r.db.ExecContext(ctx, query, cutoffTime)
	if err != nil {
		return 0, fmt.Errorf("删除过期失败订单失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return 0, fmt.Errorf("获取删除行数失败: %w", err)
	}

	return int(rowsAffected), nil
}

// DeleteFailedOrder 删除失败订单
func (r *PostgresOrderRepository) DeleteFailedOrder(ctx context.Context, orderNo string, userID string) error {
	// 构建SQL查询：只能删除属于指定用户且状态为failed的订单
	query := `
		DELETE FROM order_records 
		WHERE (order_no = $1 OR platform_order_no = $1) 
		  AND user_id = $2 
		  AND status = 'failed'
	`

	result, err := r.db.ExecContext(ctx, query, orderNo, userID)
	if err != nil {
		r.logger.Error("删除失败订单SQL执行失败",
			zap.String("order_no", orderNo),
			zap.String("user_id", userID),
			zap.Error(err))
		return fmt.Errorf("删除失败订单失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取删除行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("未找到可删除的失败订单，订单号: %s", orderNo)
	}

	r.logger.Info("成功删除失败订单",
		zap.String("order_no", orderNo),
		zap.String("user_id", userID),
		zap.Int64("rows_affected", rowsAffected))

	return nil
}

// getFailureReasonDescription 获取失败原因描述
func (r *PostgresOrderRepository) getFailureReasonDescription(reason string) string {
	descriptions := map[string]string{
		model.FailureReasonValidation: "参数验证失败",
		model.FailureReasonBalance:    "余额不足",
		model.FailureReasonProvider:   "供应商错误",
		model.FailureReasonPrice:      "价格验证失败",
		model.FailureReasonNetwork:    "网络错误",
		model.FailureReasonSystem:     "系统错误",
		model.FailureReasonTimeout:    "请求超时",
		model.FailureReasonUnknown:    "未知错误",
	}

	if desc, ok := descriptions[reason]; ok {
		return desc
	}
	return "未知原因"
}

// =====================================================
// 🔥 平台订单号相关方法实现
// =====================================================

// FindByPlatformOrderNo 通过平台订单号查询订单记录
func (r *PostgresOrderRepository) FindByPlatformOrderNo(ctx context.Context, platformOrderNo string, userID string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			-- 🔥 修复：添加失败订单相关字段
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_source, '') as failure_source,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry,
			price_validation_provider_price,
			price_validation_system_price,
			price_validation_profit_status,
			price_validation_query_status,
			price_validation_query_time,
			price_validation_error_message,
			price_validation_supported
		FROM order_records
		WHERE platform_order_no = $1
	`

	// 如果指定了用户ID，添加用户过滤
	args := []interface{}{platformOrderNo}
	if userID != "" {
		query += " AND user_id = $2"
		args = append(args, userID)
	}

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, args...).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData, &order.OrderCode,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
		// 🔥 修复：添加失败订单相关字段的扫描（包含缺失的failure_source字段）
		&order.FailureReason, &order.FailureMessage, &order.FailureStage,
		&order.FailureSource, &order.FailureTime, &order.CanRetry,
		&order.PriceValidationProviderPrice,
		&order.PriceValidationSystemPrice,
		&order.PriceValidationProfitStatus,
		&order.PriceValidationQueryStatus,
		&order.PriceValidationQueryTime,
		&order.PriceValidationErrorMessage,
		&order.PriceValidationSupported,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到平台订单号为 %s 的订单", platformOrderNo)
		}
		return nil, fmt.Errorf("查询平台订单号失败: %w", err)
	}

	r.logger.Debug("通过平台订单号查询订单成功",
		zap.String("platform_order_no", platformOrderNo),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.String("user_id", order.UserID))

	return &order, nil
}

// FindByTrackingNo 通过运单号查询订单记录
func (r *PostgresOrderRepository) FindByTrackingNo(ctx context.Context, trackingNo string) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			-- 🔥 修复：添加失败订单相关字段
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_source, '') as failure_source,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry,
			price_validation_provider_price,
			price_validation_system_price,
			price_validation_profit_status,
			price_validation_query_status,
			price_validation_query_time,
			price_validation_error_message,
			price_validation_supported
		FROM order_records
		WHERE tracking_no = $1
		ORDER BY created_at DESC
		LIMIT 1
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, trackingNo).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData, &order.OrderCode,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee,
		&order.OverweightFee, &order.UnderweightFee, &order.WeightAdjustmentReason,
		&order.BillingStatus,
		&order.OrderVolume, &order.ActualWeight, &order.ActualVolume,
		&order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
		// 🔥 修复：添加失败订单相关字段的扫描
		&order.FailureReason, &order.FailureMessage, &order.FailureStage,
		&order.FailureSource, &order.FailureTime, &order.CanRetry,
		&order.PriceValidationProviderPrice,
		&order.PriceValidationSystemPrice,
		&order.PriceValidationProfitStatus,
		&order.PriceValidationQueryStatus,
		&order.PriceValidationQueryTime,
		&order.PriceValidationErrorMessage,
		&order.PriceValidationSupported,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到运单号为 %s 的订单", trackingNo)
		}
		return nil, fmt.Errorf("查询运单号失败: %w", err)
	}

	r.logger.Debug("通过运单号查询订单成功",
		zap.String("tracking_no", trackingNo),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.String("user_id", order.UserID))

	return &order, nil
}

// FindByAnyIdentifierOptimized 性能优化：使用UNION查询支持多种标识符类型
func (r *PostgresOrderRepository) FindByAnyIdentifierOptimized(ctx context.Context, identifier string, userID string) (*model.OrderRecord, error) {
	if identifier == "" {
		return nil, fmt.Errorf("订单标识符不能为空")
	}

	// 使用UNION查询，按优先级排序（平台订单号 > 客户订单号 > 供应商订单号 > 运单号）
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry,
			price_validation_provider_price,
			price_validation_system_price,
			price_validation_profit_status,
			price_validation_query_status,
			price_validation_query_time,
			price_validation_error_message,
			price_validation_supported,
			1 as priority
		FROM order_records
		WHERE platform_order_no = $1 AND ($2 = '' OR user_id = $2)
		
		UNION ALL
		
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry,
			price_validation_provider_price,
			price_validation_system_price,
			price_validation_profit_status,
			price_validation_query_status,
			price_validation_query_time,
			price_validation_error_message,
			price_validation_supported,
			2 as priority
		FROM order_records
		WHERE customer_order_no = $1 AND ($2 = '' OR user_id = $2)
		
		UNION ALL
		
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry,
			price_validation_provider_price,
			price_validation_system_price,
			price_validation_profit_status,
			price_validation_query_status,
			price_validation_query_time,
			price_validation_error_message,
			price_validation_supported,
			3 as priority
		FROM order_records
		WHERE order_no = $1 AND ($2 = '' OR user_id = $2)
		
		UNION ALL
		
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, express_type, product_type,
			provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code,
			COALESCE(failure_reason, '') as failure_reason,
			COALESCE(failure_message, '') as failure_message,
			COALESCE(failure_stage, '') as failure_stage,
			COALESCE(failure_time::text, '') as failure_time,
			COALESCE(can_retry, false) as can_retry,
			price_validation_provider_price,
			price_validation_system_price,
			price_validation_profit_status,
			price_validation_query_status,
			price_validation_query_time,
			price_validation_error_message,
			price_validation_supported,
			4 as priority
		FROM order_records
		WHERE tracking_no = $1 AND ($2 = '' OR user_id = $2)
		
		ORDER BY priority LIMIT 1
	`

	var order model.OrderRecord
	var priority int
	err := r.db.QueryRowContext(ctx, query, identifier, userID).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
		&order.PackageInfo, &order.RequestData, &order.ResponseData, &order.OrderCode,
		&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
		&order.ActualFee, &order.InsuranceFee, &order.OverweightFee, &order.UnderweightFee,
		&order.WeightAdjustmentReason, &order.BillingStatus, &order.OrderVolume,
		&order.ActualWeight, &order.ActualVolume, &order.ChargedWeight,
		&order.CourierName, &order.CourierPhone, &order.CourierCode,
		&order.StationName, &order.PickupCode,
		&order.FailureReason, &order.FailureMessage, &order.FailureStage,
		&order.FailureTime, &order.CanRetry,
		&order.PriceValidationProviderPrice,
		&order.PriceValidationSystemPrice,
		&order.PriceValidationProfitStatus,
		&order.PriceValidationQueryStatus,
		&order.PriceValidationQueryTime,
		&order.PriceValidationErrorMessage,
		&order.PriceValidationSupported,
		&priority,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到标识符为 %s 的订单", identifier)
		}
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	// 记录查询类型日志
	queryType := "unknown"
	switch priority {
	case 1:
		queryType = "platform_order_no"
	case 2:
		queryType = "customer_order_no"
	case 3:
		queryType = "provider_order_no"
	case 4:
		queryType = "tracking_no"
	}

	r.logger.Debug("优化查询订单成功",
		zap.String("identifier", identifier),
		zap.String("query_type", queryType),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.String("user_id", order.UserID))

	return &order, nil
}

// FindTimeoutCancellingOrders 查找超时的取消中订单
func (r *PostgresOrderRepository) FindTimeoutCancellingOrders(ctx context.Context, provider string, timeout time.Duration) ([]*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no,
			express_type, product_type, provider, status, weight, price, sender_info, receiver_info,
			package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
			COALESCE(actual_fee, 0) as actual_fee,
			COALESCE(insurance_fee, 0) as insurance_fee,
			COALESCE(overweight_fee, 0) as overweight_fee,
			COALESCE(underweight_fee, 0) as underweight_fee,
			COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
			COALESCE(billing_status, 'pending') as billing_status,
			COALESCE(order_volume, 0) as order_volume,
			COALESCE(actual_weight, 0) as actual_weight,
			COALESCE(actual_volume, 0) as actual_volume,
			COALESCE(charged_weight, 0) as charged_weight,
			COALESCE(courier_name, '') as courier_name,
			COALESCE(courier_phone, '') as courier_phone,
			COALESCE(courier_code, '') as courier_code,
			COALESCE(station_name, '') as station_name,
			COALESCE(pickup_code, '') as pickup_code
		FROM order_records
		WHERE provider = $1
		  AND status = 'cancelling'
		  AND updated_at < NOW() - INTERVAL '%d seconds'
		ORDER BY updated_at ASC
	`

	formattedQuery := fmt.Sprintf(query, int(timeout.Seconds()))

	rows, err := r.db.QueryContext(ctx, formattedQuery, provider)
	if err != nil {
		return nil, fmt.Errorf("查询超时取消订单失败: %w", err)
	}
	defer rows.Close()

	var orders []*model.OrderRecord
	for rows.Next() {
		var order model.OrderRecord
		var senderInfo, receiverInfo, packageInfo, requestData, responseData string

		var orderNo string
		err := rows.Scan(
			&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &orderNo, &order.TrackingNo,
			&order.ExpressType, &order.ProductType, &order.Provider, &order.Status, &order.Weight, &order.Price,
			&senderInfo, &receiverInfo, &packageInfo, &requestData, &responseData, &order.OrderCode,
			&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
			&order.ActualFee, &order.InsuranceFee, &order.OverweightFee, &order.UnderweightFee,
			&order.WeightAdjustmentReason, &order.BillingStatus, &order.OrderVolume,
			&order.ActualWeight, &order.ActualVolume, &order.ChargedWeight,
			&order.CourierName, &order.CourierPhone, &order.CourierCode,
			&order.StationName, &order.PickupCode,
		)
		if err != nil {
			return nil, fmt.Errorf("扫描超时取消订单失败: %w", err)
		}

		// 设置兼容性字段
		if order.PlatformOrderNo != "" {
			order.OrderNo = order.PlatformOrderNo
		} else {
			order.OrderNo = orderNo
		}

		// 处理时区
		order.CreatedAt = util.EnsureBeijingTimezone(order.CreatedAt)
		order.UpdatedAt = util.EnsureBeijingTimezone(order.UpdatedAt)

		orders = append(orders, &order)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("迭代超时取消订单失败: %w", err)
	}

	return orders, nil
}

// CountCancellingOrdersByProvider 统计供应商的取消中订单数量
func (r *PostgresOrderRepository) CountCancellingOrdersByProvider(ctx context.Context, provider string) (int, error) {
	var count int
	query := `SELECT COUNT(*) FROM order_records WHERE provider = $1 AND status = 'cancelling'`
	err := r.db.QueryRowContext(ctx, query, provider).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("统计供应商取消中订单失败: %w", err)
	}
	return count, nil
}

// CountTimeoutCancellingOrdersByProvider 统计供应商的超时取消中订单数量
func (r *PostgresOrderRepository) CountTimeoutCancellingOrdersByProvider(ctx context.Context, provider string, timeout time.Duration) (int, error) {
	var count int
	query := `
		SELECT COUNT(*)
		FROM order_records
		WHERE provider = $1
		  AND status = 'cancelling'
		  AND updated_at < NOW() - INTERVAL '%d seconds'
	`
	formattedQuery := fmt.Sprintf(query, int(timeout.Seconds()))
	err := r.db.QueryRowContext(ctx, formattedQuery, provider).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("统计供应商超时取消中订单失败: %w", err)
	}
	return count, nil
}

// ListWithCursorPagination 游标分页查询，性能优化替代OFFSET分页
func (r *PostgresOrderRepository) ListWithCursorPagination(ctx context.Context, userID string, cursor string, limit int) ([]*model.OrderRecord, string, error) {
	if limit <= 0 || limit > 100 {
		limit = 20 // 默认限制
	}

	var query string
	var args []interface{}

	if cursor == "" {
		// 首次查询，按创建时间降序
		query = `
			SELECT
				id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, 
				express_type, product_type, provider, status, weight, price, sender_info, receiver_info,
				package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
				COALESCE(actual_fee, 0) as actual_fee,
				COALESCE(insurance_fee, 0) as insurance_fee,
				COALESCE(overweight_fee, 0) as overweight_fee,
				COALESCE(underweight_fee, 0) as underweight_fee,
				COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
				COALESCE(billing_status, 'pending') as billing_status,
				COALESCE(order_volume, 0) as order_volume,
				COALESCE(actual_weight, 0) as actual_weight,
				COALESCE(actual_volume, 0) as actual_volume,
				COALESCE(charged_weight, 0) as charged_weight,
				COALESCE(courier_name, '') as courier_name,
				COALESCE(courier_phone, '') as courier_phone,
				COALESCE(courier_code, '') as courier_code,
				COALESCE(station_name, '') as station_name,
				COALESCE(pickup_code, '') as pickup_code,
				COALESCE(failure_reason, '') as failure_reason,
				COALESCE(failure_message, '') as failure_message,
				COALESCE(failure_stage, '') as failure_stage,
				COALESCE(failure_time::text, '') as failure_time,
				COALESCE(can_retry, false) as can_retry,
				price_validation_provider_price,
				price_validation_system_price,
				price_validation_profit_status,
				price_validation_query_status,
				price_validation_query_time,
				price_validation_error_message,
				price_validation_supported
			FROM order_records
			WHERE user_id = $1
			ORDER BY created_at DESC, id DESC
			LIMIT $2
		`
		args = []interface{}{userID, limit + 1} // +1 用于判断是否有下一页
	} else {
		// 使用游标查询
		query = `
			SELECT
				id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no, 
				express_type, product_type, provider, status, weight, price, sender_info, receiver_info,
				package_info, request_data, response_data, order_code, created_at, updated_at, task_id, poll_token, user_id,
				COALESCE(actual_fee, 0) as actual_fee,
				COALESCE(insurance_fee, 0) as insurance_fee,
				COALESCE(overweight_fee, 0) as overweight_fee,
				COALESCE(underweight_fee, 0) as underweight_fee,
				COALESCE(weight_adjustment_reason, '') as weight_adjustment_reason,
				COALESCE(billing_status, 'pending') as billing_status,
				COALESCE(order_volume, 0) as order_volume,
				COALESCE(actual_weight, 0) as actual_weight,
				COALESCE(actual_volume, 0) as actual_volume,
				COALESCE(charged_weight, 0) as charged_weight,
				COALESCE(courier_name, '') as courier_name,
				COALESCE(courier_phone, '') as courier_phone,
				COALESCE(courier_code, '') as courier_code,
				COALESCE(station_name, '') as station_name,
				COALESCE(pickup_code, '') as pickup_code,
				COALESCE(failure_reason, '') as failure_reason,
				COALESCE(failure_message, '') as failure_message,
				COALESCE(failure_stage, '') as failure_stage,
				COALESCE(failure_time::text, '') as failure_time,
				COALESCE(can_retry, false) as can_retry,
				price_validation_provider_price,
				price_validation_system_price,
				price_validation_profit_status,
				price_validation_query_status,
				price_validation_query_time,
				price_validation_error_message,
				price_validation_supported
			FROM order_records
			WHERE user_id = $1 AND created_at < $2
			ORDER BY created_at DESC, id DESC
			LIMIT $3
		`
		args = []interface{}{userID, cursor, limit + 1}
	}

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, "", fmt.Errorf("游标分页查询失败: %w", err)
	}
	defer rows.Close()

	var orders []*model.OrderRecord
	for rows.Next() {
		var order model.OrderRecord
		err := rows.Scan(
			&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
			&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
			&order.Weight, &order.Price, &order.SenderInfo, &order.ReceiverInfo,
			&order.PackageInfo, &order.RequestData, &order.ResponseData, &order.OrderCode,
			&order.CreatedAt, &order.UpdatedAt, &order.TaskId, &order.PollToken, &order.UserID,
			&order.ActualFee, &order.InsuranceFee, &order.OverweightFee, &order.UnderweightFee,
			&order.WeightAdjustmentReason, &order.BillingStatus, &order.OrderVolume,
			&order.ActualWeight, &order.ActualVolume, &order.ChargedWeight,
			&order.CourierName, &order.CourierPhone, &order.CourierCode,
			&order.StationName, &order.PickupCode,
			&order.FailureReason, &order.FailureMessage, &order.FailureStage,
			&order.FailureTime, &order.CanRetry,
			&order.PriceValidationProviderPrice,
			&order.PriceValidationSystemPrice,
			&order.PriceValidationProfitStatus,
			&order.PriceValidationQueryStatus,
			&order.PriceValidationQueryTime,
			&order.PriceValidationErrorMessage,
			&order.PriceValidationSupported,
		)
		if err != nil {
			return nil, "", fmt.Errorf("扫描订单数据失败: %w", err)
		}
		orders = append(orders, &order)
	}

	if err := rows.Err(); err != nil {
		return nil, "", fmt.Errorf("游标分页查询行错误: %w", err)
	}

	// 判断是否有下一页并设置下一页游标
	var nextCursor string
	if len(orders) > limit {
		// 有下一页，移除多余的记录并设置游标
		orders = orders[:limit]
		nextCursor = orders[len(orders)-1].CreatedAt.Format(time.RFC3339Nano)
	}

	r.logger.Debug("游标分页查询成功",
		zap.String("user_id", userID),
		zap.String("cursor", cursor),
		zap.Int("limit", limit),
		zap.Int("result_count", len(orders)),
		zap.String("next_cursor", nextCursor))

	return orders, nextCursor, nil
}

// FindByIdLightweight 轻量级查询，只返回必要字段，性能优化
func (r *PostgresOrderRepository) FindByIdLightweight(ctx context.Context, id int64) (*model.OrderRecord, error) {
	query := `
		SELECT
			id, COALESCE(platform_order_no, '') as platform_order_no, customer_order_no, order_no, tracking_no,
			express_type, product_type, provider, status, weight, price, user_id, created_at, updated_at
		FROM order_records
		WHERE id = $1
	`

	var order model.OrderRecord
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&order.ID, &order.PlatformOrderNo, &order.CustomerOrderNo, &order.OrderNo, &order.TrackingNo,
		&order.ExpressType, &order.ProductType, &order.Provider, &order.Status,
		&order.Weight, &order.Price, &order.UserID, &order.CreatedAt, &order.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到ID为 %d 的订单", id)
		}
		return nil, fmt.Errorf("轻量级查询订单失败: %w", err)
	}

	r.logger.Debug("轻量级查询订单成功",
		zap.Int64("id", id),
		zap.String("customer_order_no", order.CustomerOrderNo),
		zap.String("user_id", order.UserID))

	return &order, nil
}
