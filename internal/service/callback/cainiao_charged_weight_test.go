package callback

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/your-org/go-kuaidi/internal/model"
	"go.uber.org/zap"
)

// TestCainiaoChargedWeightHandling 测试菜鸟计费重量处理
func TestCainiaoChargedWeightHandling(t *testing.T) {
	logger := zap.NewNop()
	standardizer := NewCallbackStandardizer(nil, logger)

	tests := []struct {
		name            string
		basePrice       string // 菜鸟返回的价格（分）
		weight          string // 菜鸟返回的重量（克）
		expectedFee     float64
		expectedWeight  float64
		expectedCharged float64
	}{
		{
			name:            "正常计费重量",
			basePrice:       "1180", // 11.80元
			weight:          "4000", // 4000克 = 4kg
			expectedFee:     11.80,
			expectedWeight:  4.0,
			expectedCharged: 4.0, // 计费重量应该等于实际重量
		},
		{
			name:            "零重量处理",
			basePrice:       "500", // 5.00元
			weight:          "0",   // 0克
			expectedFee:     5.00,
			expectedWeight:  0.0,
			expectedCharged: 1.0, // 最小计费重量1kg
		},
		{
			name:            "小数重量",
			basePrice:       "890",  // 8.90元
			weight:          "1500", // 1500克 = 1.5kg
			expectedFee:     8.90,
			expectedWeight:  1.5,
			expectedCharged: 1.5,
		},
		{
			name:            "大重量包裹",
			basePrice:       "2500",  // 25.00元
			weight:          "10000", // 10000克 = 10kg
			expectedFee:     25.00,
			expectedWeight:  10.0,
			expectedCharged: 10.0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 构建菜鸟回调数据
			parsedData := &model.ParsedCallbackData{
				Type:            "billing_updated",
				CustomerOrderNo: "test-order",
				TrackingNo:      "test-tracking",
				Data: map[string]any{
					"event_type": "COURIER_CHECK_BILL_SUCCESS",
					"order_event": map[string]any{
						"eventData": map[string]any{
							"basePrice": tt.basePrice,
							"weight":    tt.weight,
						},
					},
				},
				Timestamp: time.Now(),
			}

			// 调用标准化函数
			result, err := standardizer.standardizeCainiaoBillingUpdate(
				parsedData,
				parsedData.Data.(map[string]any),
				map[string]interface{}{},
				parsedData.Data.(map[string]any)["order_event"].(map[string]any),
			)

			// 验证结果
			require.NoError(t, err, "标准化不应该失败")
			require.NotNil(t, result, "结果不应该为nil")

			// 验证事件类型
			assert.Equal(t, model.EventTypeBillingUpdated, result.EventType)
			assert.Equal(t, model.CallbackProviderCainiao, result.Provider)

			// 验证计费数据
			billingData, ok := result.Data.(*model.BillingUpdatedData)
			require.True(t, ok, "Data应该是BillingUpdatedData类型")

			// 🔥 关键验证：计费重量必须被正确设置
			assert.Equal(t, tt.expectedCharged, billingData.ChargedWeight,
				"ChargedWeight应该被正确设置为 %.1f，实际为 %.1f",
				tt.expectedCharged, billingData.ChargedWeight)

			assert.Equal(t, tt.expectedWeight, billingData.Weight,
				"Weight应该被正确设置为 %.1f，实际为 %.1f",
				tt.expectedWeight, billingData.Weight)

			assert.Equal(t, tt.expectedFee, billingData.TotalFee,
				"TotalFee应该被正确设置为 %.2f，实际为 %.2f",
				tt.expectedFee, billingData.TotalFee)

			// 验证费用明细
			if tt.expectedFee > 0 {
				require.Len(t, billingData.FeeDetails, 1, "应该有一个费用明细")
				assert.Equal(t, "freight", billingData.FeeDetails[0].FeeType)
				assert.Equal(t, "运费", billingData.FeeDetails[0].FeeDesc)
				assert.Equal(t, tt.expectedFee, billingData.FeeDetails[0].Amount)
			}
		})
	}
}

// TestCainiaoChargedWeightZeroHandling 测试菜鸟零重量的特殊处理
func TestCainiaoChargedWeightZeroHandling(t *testing.T) {
	logger := zap.NewNop()
	standardizer := NewCallbackStandardizer(nil, logger)

	// 测试完全没有重量信息的情况
	parsedData := &model.ParsedCallbackData{
		Type:            "billing_updated",
		CustomerOrderNo: "test-order-no-weight",
		TrackingNo:      "test-tracking-no-weight",
		Data: map[string]any{
			"event_type": "COURIER_CHECK_BILL_SUCCESS",
			"order_event": map[string]any{
				"eventData": map[string]any{
					"basePrice": "1000", // 10.00元
					// 没有weight字段
				},
			},
		},
		Timestamp: time.Now(),
	}

	result, err := standardizer.standardizeCainiaoBillingUpdate(
		parsedData,
		parsedData.Data.(map[string]any),
		map[string]interface{}{},
		parsedData.Data.(map[string]any)["order_event"].(map[string]any),
	)

	require.NoError(t, err)
	require.NotNil(t, result)

	billingData, ok := result.Data.(*model.BillingUpdatedData)
	require.True(t, ok)

	// 🔥 关键验证：没有重量信息时，计费重量应该设置为1kg
	assert.Equal(t, 0.0, billingData.Weight, "Weight应该为0（没有重量信息）")
	assert.Equal(t, 1.0, billingData.ChargedWeight, "ChargedWeight应该设置为最小值1kg")
	assert.Equal(t, 10.0, billingData.TotalFee, "TotalFee应该正确解析")
}
