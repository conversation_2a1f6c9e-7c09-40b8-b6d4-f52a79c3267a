package callback

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// TestKuaidiNiaoChargedWeightExtraction 测试快递鸟计费重量提取
func TestKuaidiNiaoChargedWeightExtraction(t *testing.T) {
	logger := zap.NewNop()
	adapter := &KuaidiNiaoCallbackAdapter{logger: logger}

	tests := []struct {
		name           string
		rawData        map[string]interface{}
		expectedWeight float64
		description    string
	}{
		{
			name: "使用ActualWeight",
			rawData: map[string]interface{}{
				"ActualWeight":     25.0,
				"Weight":           20.0,
				"FirstWeight":      1.0,
				"ContinuousWeight": 19.0,
			},
			expectedWeight: 25.0,
			description:    "应该优先使用ActualWeight",
		},
		{
			name: "使用Weight（无ActualWeight）",
			rawData: map[string]interface{}{
				"Weight":           20.0,
				"FirstWeight":      1.0,
				"ContinuousWeight": 19.0,
			},
			expectedWeight: 20.0,
			description:    "当没有ActualWeight时，应该使用Weight",
		},
		{
			name: "使用计算重量",
			rawData: map[string]interface{}{
				"FirstWeight":      1.0,
				"ContinuousWeight": 24.0,
			},
			expectedWeight: 25.0,
			description:    "应该使用首重+续重计算",
		},
		{
			name: "使用体积重量",
			rawData: map[string]interface{}{
				"VolumeWeight": 30.0,
			},
			expectedWeight: 30.0,
			description:    "应该使用体积重量作为兜底",
		},
		{
			name: "无有效重量",
			rawData: map[string]interface{}{
				"ActualWeight":     0.0,
				"Weight":           0.0,
				"FirstWeight":      0.0,
				"ContinuousWeight": 0.0,
				"VolumeWeight":     0.0,
			},
			expectedWeight: 0.0,
			description:    "所有重量都为0时，应该返回0",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := adapter.extractChargedWeight(tt.rawData)
			assert.Equal(t, tt.expectedWeight, result, tt.description)
		})
	}
}

// TestBillingUpdatedDataSerialization 测试BillingUpdatedData序列化
func TestBillingUpdatedDataSerialization(t *testing.T) {
	tests := []struct {
		name          string
		chargedWeight float64
		shouldContain string
		description   string
	}{
		{
			name:          "ChargedWeight为正数",
			chargedWeight: 25.0,
			shouldContain: `"charged_weight":25`,
			description:   "正数应该被正确序列化",
		},
		{
			name:          "ChargedWeight为0",
			chargedWeight: 0.0,
			shouldContain: `"charged_weight":0`,
			description:   "0值也应该被序列化（修复后）",
		},
		{
			name:          "ChargedWeight为小数",
			chargedWeight: 25.5,
			shouldContain: `"charged_weight":25.5`,
			description:   "小数应该被正确序列化",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data := &model.BillingUpdatedData{
				Weight:        25.0,
				ChargedWeight: tt.chargedWeight,
				Cost:          28.29,
				TotalFee:      28.29,
				UpdateTime:    time.Now(),
			}

			jsonBytes, err := json.Marshal(data)
			assert.NoError(t, err, "序列化不应该失败")

			jsonStr := string(jsonBytes)
			assert.Contains(t, jsonStr, tt.shouldContain, tt.description)
			assert.Contains(t, jsonStr, "charged_weight", "JSON应该包含charged_weight字段")
		})
	}
}

// TestKuaidiNiaoStandardizeBillingUpdate 测试快递鸟计费更新标准化
func TestKuaidiNiaoStandardizeBillingUpdate(t *testing.T) {
	logger := zap.NewNop()
	adapter := &KuaidiNiaoCallbackAdapter{logger: logger}

	// 模拟快递鸟回调数据
	rawData := map[string]interface{}{
		"ActualWeight": 25.0,
		"Weight":       25.0,
		"Cost":         28.29,
		"TotalFee":     28.29,
		"Volume":       0.0,
	}

	parsedData := &model.ParsedCallbackData{
		Type:            "billing_updated",
		OrderNo:         "GK20250813000004035",
		CustomerOrderNo: "9393565_157",
		TrackingNo:      "773371768313960",
		Data:            rawData,
		Timestamp:       time.Now(),
	}

	result, err := adapter.standardizeBillingUpdate(parsedData)
	assert.NoError(t, err, "标准化不应该失败")
	assert.NotNil(t, result, "结果不应该为nil")

	// 验证标准化结果
	assert.Equal(t, "billing_updated", result.EventType)
	assert.Equal(t, "GK20250813000004035", result.OrderNo)
	assert.Equal(t, "kuaidiniao", result.Provider)

	// 验证计费数据
	billingData, ok := result.Data.(*model.BillingUpdatedData)
	assert.True(t, ok, "Data应该是BillingUpdatedData类型")
	assert.Equal(t, 25.0, billingData.ChargedWeight, "ChargedWeight应该被正确设置")
	assert.Equal(t, 25.0, billingData.Weight, "Weight应该被正确设置")
	assert.Equal(t, 28.29, billingData.TotalFee, "TotalFee应该被正确设置")

	// 验证JSON序列化
	jsonBytes, err := json.Marshal(billingData)
	assert.NoError(t, err, "序列化不应该失败")
	jsonStr := string(jsonBytes)
	assert.Contains(t, jsonStr, "charged_weight", "JSON应该包含charged_weight字段")
	assert.Contains(t, jsonStr, `"charged_weight":25`, "ChargedWeight应该被正确序列化")
}

// TestConvertToFloat64 测试类型转换函数
func TestConvertToFloat64(t *testing.T) {
	logger := zap.NewNop()
	adapter := &KuaidiNiaoCallbackAdapter{logger: logger}

	tests := []struct {
		name     string
		value    interface{}
		expected float64
	}{
		{"float64", 25.0, 25.0},
		{"float32", float32(25.0), 25.0},
		{"int", 25, 25.0},
		{"int64", int64(25), 25.0},
		{"string_valid", "25.0", 25.0},
		{"string_invalid", "invalid", 0.0},
		{"nil", nil, 0.0},
		{"unsupported", []int{1, 2, 3}, 0.0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := adapter.convertToFloat64(tt.value, "test_field")
			assert.Equal(t, tt.expected, result)
		})
	}
}

// TestRealWorldKuaidiNiaoData 测试真实的快递鸟数据
func TestRealWorldKuaidiNiaoData(t *testing.T) {
	logger := zap.NewNop()
	adapter := &KuaidiNiaoCallbackAdapter{logger: logger}

	// 这是从数据库中提取的真实快递鸟回调数据
	realRawData := map[string]interface{}{
		"ShipperCode":                      "STO",
		"ActualWeight":                     25.0,
		"FirstWeightAmount":                "4.29",
		"Cost":                             28.29,
		"Success":                          true,
		"Reason":                           "已揽件",
		"VolumeWeight":                     0.0,
		"IsSubsectionContinuousWeightPrice": 0,
		"OperateType":                      2,
		"ContinuousWeight":                 24.0,
		"CreateTime":                       "2025-08-13 18:03:59",
		"ContinuousWeightAmount":           "24.00",
		"OrderCode":                        "GK20250813000004035",
		"EBusinessID":                      "1778716",
		"CallRequestType":                  "1801",
		"Weight":                           25.0,
		"ContinuousWeightPrice":            1.0,
		"LogisticCode":                     "773371768313960",
		"TotalFee":                         28.29,
		"Volume":                           0.0,
		"State":                            "601",
		"FirstWeight":                      1.0,
		"OtherFeeDetail":                   "{}",
		"OtherFee":                         "0.00",
	}

	// 测试计费重量提取
	chargedWeight := adapter.extractChargedWeight(realRawData)
	assert.Equal(t, 25.0, chargedWeight, "应该正确提取ActualWeight作为计费重量")

	// 测试完整的标准化流程
	parsedData := &model.ParsedCallbackData{
		Type:            "billing_updated",
		OrderNo:         "GK20250813000004035",
		CustomerOrderNo: "9393565_157",
		TrackingNo:      "773371768313960",
		Data:            realRawData,
		Timestamp:       time.Now(),
	}

	result, err := adapter.standardizeBillingUpdate(parsedData)
	assert.NoError(t, err, "真实数据标准化不应该失败")

	billingData, ok := result.Data.(*model.BillingUpdatedData)
	assert.True(t, ok, "Data应该是BillingUpdatedData类型")
	assert.Equal(t, 25.0, billingData.ChargedWeight, "真实数据的ChargedWeight应该被正确提取")

	// 验证JSON序列化不会丢失charged_weight字段
	jsonBytes, err := json.Marshal(billingData)
	assert.NoError(t, err, "真实数据序列化不应该失败")
	jsonStr := string(jsonBytes)
	assert.Contains(t, jsonStr, "charged_weight", "真实数据JSON应该包含charged_weight字段")
	assert.Contains(t, jsonStr, `"charged_weight":25`, "真实数据ChargedWeight应该被正确序列化")
}
