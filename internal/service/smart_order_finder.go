package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"go.uber.org/zap"
)

// SmartOrderFinder 生产级智能订单查找服务
// 高并发安全，高性能，高准确性的订单查找解决方案
type SmartOrderFinder struct {
	orderRepository repository.OrderRepository
	logger          *zap.Logger

	// 缓存层 - 避免重复查询
	cache    map[string]*CachedOrderResult
	cacheMu  sync.RWMutex
	cacheExp time.Duration

	// 查询统计
	stats   *QueryStats
	statsMu sync.RWMutex
}

// CachedOrderResult 缓存的订单查询结果
type CachedOrderResult struct {
	Order     *model.OrderRecord
	Error     error
	ExpiresAt time.Time
	QueryType string
}

// QueryStats 查询统计信息
type QueryStats struct {
	TotalQueries      int64
	CacheHits         int64
	CacheMisses       int64
	PlatformOrderHits int64
	CustomerOrderHits int64
	ProviderOrderHits int64
	TrackingNoHits    int64
	Errors            int64
}

// NewSmartOrderFinder 创建生产级智能订单查找服务
func NewSmartOrderFinder(orderRepository repository.OrderRepository, logger *zap.Logger) *SmartOrderFinder {
	return &SmartOrderFinder{
		orderRepository: orderRepository,
		logger:          logger,
		cache:           make(map[string]*CachedOrderResult),
		cacheExp:        2 * time.Minute, // 2分钟缓存
		stats:           &QueryStats{},
	}
}

// FindOrderByAnyIdentifier 高并发安全的订单查找
func (s *SmartOrderFinder) FindOrderByAnyIdentifier(ctx context.Context, identifier string, userID string) (*model.OrderRecord, error) {
	if identifier == "" {
		return nil, fmt.Errorf("订单标识符不能为空")
	}

	// 更新统计
	s.updateStats(func(stats *QueryStats) {
		stats.TotalQueries++
	})

	// 生成缓存键
	cacheKey := fmt.Sprintf("%s:%s", identifier, userID)

	// 检查缓存
	if cachedResult := s.getFromCache(cacheKey); cachedResult != nil {
		s.updateStats(func(stats *QueryStats) {
			stats.CacheHits++
		})
		s.logger.Debug("🎯 缓存命中",
			zap.String("identifier", identifier),
			zap.String("query_type", cachedResult.QueryType))
		return cachedResult.Order, cachedResult.Error
	}

	s.updateStats(func(stats *QueryStats) {
		stats.CacheMisses++
	})

	// 执行优化查询
	order, err := s.executeOptimizedQuery(ctx, identifier, userID)

	// 缓存结果
	s.setCache(cacheKey, &CachedOrderResult{
		Order:     order,
		Error:     err,
		ExpiresAt: time.Now().Add(s.cacheExp),
		QueryType: "optimized_union",
	})

	// 更新统计
	if err != nil {
		s.updateStats(func(stats *QueryStats) {
			stats.Errors++
		})
	}

	return order, err
}

// executeOptimizedQuery 执行优化的UNION查询
func (s *SmartOrderFinder) executeOptimizedQuery(ctx context.Context, identifier string, userID string) (*model.OrderRecord, error) {
	s.logger.Debug("🚀 执行优化UNION查询",
		zap.String("identifier", identifier),
		zap.String("user_id", userID))

	// 尝试使用优化的查询方法
	order, err := s.orderRepository.FindByAnyIdentifierOptimized(ctx, identifier, userID)
	if err != nil {
		s.logger.Debug("优化查询失败，回退到原始查询", zap.Error(err))
		// 回退到原始查询方法
		fallbackOrder, _, fallbackErr := s.executeQuery(ctx, identifier, userID)
		return fallbackOrder, fallbackErr
	}

	s.logger.Info("✅ 优化查询成功",
		zap.String("identifier", identifier),
		zap.String("customer_order_no", order.CustomerOrderNo))

	return order, nil
}

// executeQuery 执行实际的查询逻辑（原始方法作为回退）
func (s *SmartOrderFinder) executeQuery(ctx context.Context, identifier string, userID string) (*model.OrderRecord, string, error) {
	s.logger.Debug("🔍 执行订单查询",
		zap.String("identifier", identifier),
		zap.String("user_id", userID))

	// 1. 优先尝试平台订单号（19位，GK开头）
	if len(identifier) == 19 && strings.HasPrefix(identifier, "GK") {
		s.logger.Debug("识别为平台订单号", zap.String("platform_order_no", identifier))
		order, err := s.orderRepository.FindByPlatformOrderNo(ctx, identifier, userID)
		if err == nil {
			s.logger.Info("✅ 平台订单号查询成功",
				zap.String("platform_order_no", identifier),
				zap.String("customer_order_no", order.CustomerOrderNo))
			return order, "platform_order_no", nil
		}
		s.logger.Debug("平台订单号查询失败，尝试其他方式", zap.Error(err))
	}

	// 2. 尝试客户订单号查询
	order, err := s.orderRepository.FindByCustomerOrderNo(ctx, identifier)
	if err == nil {
		s.logger.Debug("客户订单号查询数据库成功",
			zap.String("customer_order_no", identifier),
			zap.String("found_user_id", order.UserID),
			zap.String("request_user_id", userID))

		if userID == "" || order.UserID == userID {
			s.logger.Info("✅ 客户订单号查询成功",
				zap.String("customer_order_no", identifier))
			return order, "customer_order_no", nil
		}
	} else {
		s.logger.Debug("客户订单号查询数据库失败",
			zap.String("customer_order_no", identifier),
			zap.Error(err))
		
		// 🔥 修复：客户订单号精确查找失败后，尝试模糊匹配（处理订单号带后缀的情况）
		// 如工单回调中的"9514888"需要匹配数据库中的"9514888_157"
		if fuzzyOrder, fuzzyErr := s.orderRepository.FindByCustomerOrderNoPattern(ctx, identifier+"%"); fuzzyErr == nil {
			s.logger.Info("🎯 客户订单号模糊匹配成功",
				zap.String("pattern", identifier+"*"),
				zap.String("found_customer_order_no", fuzzyOrder.CustomerOrderNo),
				zap.String("found_user_id", fuzzyOrder.UserID),
				zap.String("request_user_id", userID))
			
			if userID == "" || fuzzyOrder.UserID == userID {
				return fuzzyOrder, "customer_order_no_fuzzy", nil
			}
		} else {
			s.logger.Info("客户订单号模糊匹配也失败",
				zap.String("pattern", identifier+"%"),
				zap.Error(fuzzyErr))
		}
	}

	// 3. 尝试供应商订单号查询
	order, err = s.orderRepository.FindByOrderNo(ctx, identifier)
	if err == nil {
		s.logger.Debug("供应商订单号查询数据库成功",
			zap.String("provider_order_no", identifier),
			zap.String("found_user_id", order.UserID),
			zap.String("request_user_id", userID))

		if userID == "" || order.UserID == userID {
			s.logger.Info("✅ 供应商订单号查询成功",
				zap.String("provider_order_no", identifier))
			return order, "provider_order_no", nil
		}
	} else {
		s.logger.Debug("供应商订单号查询数据库失败",
			zap.String("provider_order_no", identifier),
			zap.Error(err))
	}

	// 4. 尝试运单号查询
	order, err = s.orderRepository.FindByTrackingNo(ctx, identifier)
	if err == nil {
		s.logger.Debug("运单号查询数据库成功",
			zap.String("tracking_no", identifier),
			zap.String("found_user_id", order.UserID),
			zap.String("request_user_id", userID))

		if userID == "" || order.UserID == userID {
			s.logger.Info("✅ 运单号查询成功",
				zap.String("tracking_no", identifier))
			return order, "tracking_no", nil
		} else {
			s.logger.Warn("运单号查询到订单但用户ID不匹配",
				zap.String("tracking_no", identifier),
				zap.String("found_user_id", order.UserID),
				zap.String("request_user_id", userID))
		}
	} else {
		s.logger.Debug("运单号查询数据库失败",
			zap.String("tracking_no", identifier),
			zap.Error(err))
	}

	s.logger.Error("❌ 所有查询方式都失败",
		zap.String("identifier", identifier),
		zap.String("user_id", userID))

	return nil, "", fmt.Errorf("订单不存在")
}

// getFromCache 从缓存获取结果
func (s *SmartOrderFinder) getFromCache(key string) *CachedOrderResult {
	s.cacheMu.RLock()
	defer s.cacheMu.RUnlock()

	if cached, exists := s.cache[key]; exists {
		if time.Now().Before(cached.ExpiresAt) {
			return cached
		}
		// 缓存过期，删除
		delete(s.cache, key)
	}
	return nil
}

// setCache 设置缓存
func (s *SmartOrderFinder) setCache(key string, result *CachedOrderResult) {
	s.cacheMu.Lock()
	defer s.cacheMu.Unlock()
	s.cache[key] = result
}

// updateStats 更新统计信息
func (s *SmartOrderFinder) updateStats(fn func(*QueryStats)) {
	s.statsMu.Lock()
	defer s.statsMu.Unlock()
	fn(s.stats)
}

// GetStats 获取查询统计信息
func (s *SmartOrderFinder) GetStats() map[string]interface{} {
	s.statsMu.RLock()
	defer s.statsMu.RUnlock()

	cacheHitRate := float64(0)
	if s.stats.TotalQueries > 0 {
		cacheHitRate = float64(s.stats.CacheHits) / float64(s.stats.TotalQueries) * 100
	}

	return map[string]interface{}{
		"total_queries":       s.stats.TotalQueries,
		"cache_hits":          s.stats.CacheHits,
		"cache_misses":        s.stats.CacheMisses,
		"cache_hit_rate":      fmt.Sprintf("%.2f%%", cacheHitRate),
		"platform_order_hits": s.stats.PlatformOrderHits,
		"customer_order_hits": s.stats.CustomerOrderHits,
		"provider_order_hits": s.stats.ProviderOrderHits,
		"tracking_no_hits":    s.stats.TrackingNoHits,
		"errors":              s.stats.Errors,
		"cache_size":          len(s.cache),
		"cache_duration":      s.cacheExp.String(),
	}
}

// ClearCache 清理缓存
func (s *SmartOrderFinder) ClearCache() {
	s.cacheMu.Lock()
	defer s.cacheMu.Unlock()
	s.cache = make(map[string]*CachedOrderResult)
}

// FindOrderByOrderNoOrTrackingNo 兼容旧接口
func (s *SmartOrderFinder) FindOrderByOrderNoOrTrackingNo(ctx context.Context, orderNo, trackingNo string, userID string) (*model.OrderRecord, error) {
	// 优先使用订单号查找
	if orderNo != "" {
		order, err := s.FindOrderByAnyIdentifier(ctx, orderNo, userID)
		if err == nil {
			return order, nil
		}
	}

	// 使用运单号查找
	if trackingNo != "" {
		return s.FindOrderByAnyIdentifier(ctx, trackingNo, userID)
	}

	return nil, fmt.Errorf("订单不存在")
}
