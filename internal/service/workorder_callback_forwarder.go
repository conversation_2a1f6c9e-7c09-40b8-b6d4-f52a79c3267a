package service

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/your-org/go-kuaidi/internal/constants"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/repository"
	"github.com/your-org/go-kuaidi/internal/util"
)

// WorkOrderCallbackForwarder 工单回调转发器
// 🔥 企业级标准：统一处理三大供应商的工单回调，确保数据流向正确
type WorkOrderCallbackForwarder struct {
	workOrderRepo    repository.WorkOrderRepository
	callbackRepo     repository.CallbackRepository
	orderRepo        repository.OrderRepository // 🔥 订单仓库，用于查询客户订单号
	smartOrderFinder *SmartOrderFinder          // 🔥 新增：智能订单查找服务
	httpClient       *http.Client
	logger           *zap.Logger
}

// NewWorkOrderCallbackForwarder 创建工单回调转发器
func NewWorkOrderCallbackForwarder(
	workOrderRepo repository.WorkOrderRepository,
	callbackRepo repository.CallbackRepository,
	orderRepo repository.OrderRepository, // 🔥 订单仓库参数
	smartOrderFinder *SmartOrderFinder, // 🔥 新增：智能订单查找服务参数
	logger *zap.Logger,
) *WorkOrderCallbackForwarder {
	return &WorkOrderCallbackForwarder{
		workOrderRepo:    workOrderRepo,
		callbackRepo:     callbackRepo,
		orderRepo:        orderRepo,        // 🔥 设置订单仓库
		smartOrderFinder: smartOrderFinder, // 🔥 新增：设置智能订单查找服务
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logger,
	}
}

// ProcessCallback 处理工单回调
// 正确的数据流向：原始数据 → 解析 → 标准化 → 内部处理 → 转发
func (f *WorkOrderCallbackForwarder) ProcessCallback(
	ctx context.Context,
	provider string,
	rawData []byte,
	headers map[string]string,
) (*model.CallbackResponse, error) {
	requestID := uuid.New().String()
	startTime := util.NowBeijing()

	f.logger.Info("接收工单回调",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.Int("data_size", len(rawData)))

	// 1. 解析原始供应商数据
	parsedData, err := f.parseProviderCallback(provider, rawData)
	if err != nil {
		f.logger.Error("解析供应商回调数据失败",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.Error(err))
		return f.buildErrorResponse(provider, "数据解析失败"), err
	}

	// 2. 查找对应的工单
	workOrder, err := f.findWorkOrder(ctx, provider, parsedData.ProviderWorkOrderID)
	if err != nil {
		f.logger.Warn("查找工单失败，可能是测试数据或工单不存在",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.String("provider_work_order_id", parsedData.ProviderWorkOrderID),
			zap.Error(err))

		// 🔥 重要：即使找不到工单，也要返回成功响应，避免供应商重复推送
		// 这是供应商回调的标准处理方式
		response := f.buildSuccessResponse(provider)

		// 记录回调数据用于调试
		f.logger.Info("回调数据（工单不存在）",
			zap.String("request_id", requestID),
			zap.String("provider", provider),
			zap.String("raw_data", string(rawData)),
			zap.Any("parsed_data", parsedData))

		return response, nil
	}

	// 🔥 3. 内部处理逻辑（新增）
	if err := f.processInternalWorkOrderLogic(ctx, workOrder, parsedData, provider, requestID); err != nil {
		f.logger.Error("内部工单处理失败",
			zap.String("request_id", requestID),
			zap.Error(err))
		// 不阻断转发流程，记录错误后继续
	}

	// 4. 标准化回调数据
	standardizedData := f.standardizeCallbackData(workOrder, parsedData, provider)

	// 5. 保存统一回调记录
	if err := f.saveUnifiedCallbackRecord(context.Background(), workOrder, parsedData, standardizedData, provider, requestID); err != nil {
		f.logger.Error("保存统一回调记录失败",
			zap.String("request_id", requestID),
			zap.String("work_order_id", workOrder.ID.String()),
			zap.Error(err))
		// 不阻断流程，继续处理
	}

	// 6. 异步转发给用户
	go f.forwardToUser(context.Background(), workOrder, standardizedData, requestID)

	// 7. 返回供应商期望的响应格式
	response := f.buildSuccessResponse(provider)

	duration := time.Since(startTime)
	f.logger.Info("工单回调处理完成",
		zap.String("request_id", requestID),
		zap.String("provider", provider),
		zap.Duration("duration", duration))

	return response, nil
}

// parseProviderCallback 解析供应商回调数据
func (f *WorkOrderCallbackForwarder) parseProviderCallback(provider string, rawData []byte) (*model.WorkOrderCallbackData, error) {
	switch provider {
	case "kuaidiniao":
		return f.parseKuaidiniaoCallback(rawData)
	case "kuaidi100":
		return f.parseKuaidi100Callback(rawData)
	case "yida":
		return f.parseYidaCallback(rawData)
	case "yuntong":
		return f.parseYuntongCallback(rawData)
	default:
		return nil, fmt.Errorf("不支持的供应商: %s", provider)
	}
}

// parseKuaidiniaoCallback 解析快递鸟工单回调
func (f *WorkOrderCallbackForwarder) parseKuaidiniaoCallback(rawData []byte) (*model.WorkOrderCallbackData, error) {
	// 🔥 快递鸟工单回调数据是URL编码的表单数据，需要先解析表单
	dataStr := string(rawData)

	// 解析URL编码的表单数据
	values, err := url.ParseQuery(dataStr)
	if err != nil {
		return nil, fmt.Errorf("解析快递鸟工单表单数据失败: %w", err)
	}

	// 提取RequestData字段
	requestData := values.Get("RequestData")
	if requestData == "" {
		return nil, fmt.Errorf("缺少RequestData字段")
	}

	// 验证RequestType是否为103（工单回调）
	requestType := values.Get("RequestType")
	if requestType != "103" {
		return nil, fmt.Errorf("非快递鸟工单回调数据: RequestType=%s", requestType)
	}

	// 解析RequestData中的JSON数据
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(requestData), &jsonData); err != nil {
		return nil, fmt.Errorf("解析RequestData JSON失败: %w", err)
	}

	// 🔥 从Data数组中提取工单处理结果（根据快递鸟官方文档5.18）
	dataArray, ok := jsonData["Data"].([]interface{})
	if !ok || len(dataArray) == 0 {
		return nil, fmt.Errorf("快递鸟工单回调缺少Data数组")
	}

	workOrderData, ok := dataArray[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("快递鸟工单数据格式错误")
	}

	// 提取工单号（TicketId - 官方文档字段名）
	var ticketId string
	if id, ok := workOrderData["TicketId"].(string); ok {
		ticketId = id
	} else if id, ok := workOrderData["TicketNumber"].(string); ok {
		// 兼容旧字段名
		ticketId = id
	} else {
		return nil, fmt.Errorf("缺少工单号")
	}

	// 提取处理结果内容（Reason - 官方文档字段名）
	var content string
	if reason, ok := workOrderData["Reason"].(string); ok {
		content = reason
	} else if dealResult, ok := workOrderData["DealResult"].(string); ok {
		// 兼容旧字段名
		content = dealResult
	}

	// 提取状态（State字段固定为"401"表示工单处理结果）
	var status int
	var committer string
	if state, ok := workOrderData["State"].(string); ok && state == "401" {
		// 根据OperateType判断处理状态：1=快递鸟处理，2=物流公司处理
		if operateType, ok := workOrderData["OperateType"].(float64); ok {
			status = 2 // 已完成（有处理结果）

			// 设置处理人
			if operateType == 1 {
				committer = "快递鸟客服"
			} else if operateType == 2 {
				committer = "物流公司客服"
			} else {
				committer = "客服"
			}
		} else {
			status = 2 // 默认已完成
			committer = "快递鸟客服"
		}
	} else {
		status = 2 // 默认已完成
		committer = "快递鸟客服"
	}

	// 提取附件信息
	var attachments []string
	if dealResultFiles, ok := workOrderData["DealResultFiles"].(string); ok && dealResultFiles != "" {
		// 处理结果附件，多个以逗号分隔
		attachments = append(attachments, strings.Split(dealResultFiles, ",")...)
	}
	if ticketPic, ok := workOrderData["TicketPic"].(string); ok && ticketPic != "" {
		// 工单提交附件，多个以逗号分隔
		attachments = append(attachments, strings.Split(ticketPic, ",")...)

	}

	return &model.WorkOrderCallbackData{
		ProviderWorkOrderID: ticketId,
		Status:              status,
		Content:             content,
		Committer:           committer,
		AttachmentURLs:      attachments,
		UpdatedAt:           util.NowBeijing(),
	}, nil
}

// parseKuaidi100Callback 解析快递100回调
func (f *WorkOrderCallbackForwarder) parseKuaidi100Callback(rawData []byte) (*model.WorkOrderCallbackData, error) {
	// 快递100使用表单格式，先转换为map
	var data map[string]interface{}
	if err := json.Unmarshal(rawData, &data); err != nil {
		return nil, fmt.Errorf("解析快递100回调数据失败: %w", err)
	}

	// 提取工单ID（避免科学计数法）
	workOrderIDVal, ok := data["workorderId"]
	if !ok {
		return nil, fmt.Errorf("缺少工单ID")
	}
	// toPlainString 将任意类型的数字/字符串安全转换为不带科学计数法的字符串
	toPlainString := func(v interface{}) string {
		switch t := v.(type) {
		case string:
			return t
		case float64:
			return strconv.FormatInt(int64(t), 10)
		case float32:
			return strconv.FormatInt(int64(t), 10)
		case int:
			return strconv.FormatInt(int64(t), 10)
		case int64:
			return strconv.FormatInt(t, 10)
		case json.Number:
			if i, err := t.Int64(); err == nil {
				return strconv.FormatInt(i, 10)
			}
			return t.String()
		default:
			return fmt.Sprintf("%v", v)
		}
	}
	providerWorkOrderID := toPlainString(workOrderIDVal)

	// 判断是工单结果回调还是留言回调
	if committer, exists := data["committer"]; exists {
		// 留言回调
		content, _ := data["content"].(string)

		// 处理附件
		var attachmentURLs []string
		if attachList, exists := data["attach"].([]interface{}); exists {
			for _, attachItem := range attachList {
				if attach, ok := attachItem.(map[string]interface{}); ok {
					if uri, exists := attach["uri"].(string); exists && uri != "" {
						attachmentURLs = append(attachmentURLs, uri)
					}
				}
			}
		}

		return &model.WorkOrderCallbackData{
			ProviderWorkOrderID: providerWorkOrderID,
			Status:              0, // 留言不改变状态
			Content:             content,
			Committer:           fmt.Sprintf("%v", committer),
			AttachmentURLs:      attachmentURLs,
			UpdatedAt:           util.NowBeijing(),
		}, nil
	} else {
		// 工单结果回调
		status := 0
		if s, ok := data["status"]; ok {
			if statusFloat, ok := s.(float64); ok {
				status = int(statusFloat)
			} else if statusStr, ok := s.(string); ok {
				if statusInt, err := strconv.Atoi(statusStr); err == nil {
					status = statusInt
				}
			}
		}

		result, _ := data["result"].(string)

		return &model.WorkOrderCallbackData{
			ProviderWorkOrderID: providerWorkOrderID,
			Status:              status,
			Content:             result,
			Committer:           "快递100客服",
			AttachmentURLs:      []string{},
			UpdatedAt:           util.NowBeijing(),
		}, nil
	}
}

// parseYidaCallback 解析易达回调
func (f *WorkOrderCallbackForwarder) parseYidaCallback(rawData []byte) (*model.WorkOrderCallbackData, error) {
	var data map[string]interface{}
	if err := json.Unmarshal(rawData, &data); err != nil {
		return nil, fmt.Errorf("解析易达回调数据失败: %w", err)
	}

	// 提取工单编号
	taskNo, ok := data["taskNo"]
	if !ok {
		return nil, fmt.Errorf("缺少工单编号")
	}

	status := 0
	if s, ok := data["status"]; ok {
		if statusFloat, ok := s.(float64); ok {
			status = int(statusFloat)
		}
	}

	content, _ := data["content"].(string)
	workOrderType := 0
	if t, ok := data["type"]; ok {
		if typeFloat, ok := t.(float64); ok {
			workOrderType = int(typeFloat)
		}
	}

	// 处理附件
	var attachmentURLs []string
	if urls, ok := data["urls"].(string); ok && urls != "" {
		// 易达的urls是逗号分隔的字符串
		for _, url := range splitString(urls, ",") {
			if url != "" {
				attachmentURLs = append(attachmentURLs, url)
			}
		}
	}

	// 构建回复人信息
	committer := "易达客服"
	if workOrderType == 1 {
		committer += " (揽收处理)"
	} else if workOrderType == 2 {
		// 从data中提取重量信息
		if dataObj, ok := data["data"].(map[string]interface{}); ok {
			if weight, ok := dataObj["weight"].(float64); ok {
				committer += fmt.Sprintf(" (核实重量: %.1f kg)", weight)
			}
		}
	}

	return &model.WorkOrderCallbackData{
		ProviderWorkOrderID: fmt.Sprintf("%v", taskNo),
		Status:              status,
		Content:             content,
		Committer:           committer,
		AttachmentURLs:      attachmentURLs,
		UpdatedAt:           util.NowBeijing(),
		WorkOrderType:       workOrderType,
	}, nil
}

// parseYuntongCallback 解析云通回调
func (f *WorkOrderCallbackForwarder) parseYuntongCallback(rawData []byte) (*model.WorkOrderCallbackData, error) {
	var outerData map[string]interface{}
	if err := json.Unmarshal(rawData, &outerData); err != nil {
		return nil, fmt.Errorf("解析云通回调数据失败: %w", err)
	}

	// 🔥 修复：云通工单回调的RequestData是JSON字符串，需要先解析
	requestDataStr, ok := outerData["RequestData"].(string)
	if !ok {
		return nil, fmt.Errorf("云通回调缺少RequestData字段")
	}

	// 解析RequestData内部的JSON
	var requestData map[string]interface{}
	if err := json.Unmarshal([]byte(requestDataStr), &requestData); err != nil {
		return nil, fmt.Errorf("解析云通RequestData失败: %w", err)
	}

	// 提取Data数组
	dataArray, ok := requestData["Data"].([]interface{})
	if !ok || len(dataArray) == 0 {
		return nil, fmt.Errorf("云通回调缺少Data数组")
	}

	// 取第一个工单数据
	workOrderData, ok := dataArray[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("云通工单数据格式错误")
	}

	// 提取工单编号
	complaintNumber, ok := workOrderData["ComplaintNumber"]
	if !ok {
		return nil, fmt.Errorf("缺少工单编号")
	}

	reason, _ := workOrderData["Reason"].(string)
	dealResult, _ := workOrderData["dealResult"].(string)

	// 处理附件
	var attachmentURLs []string
	if picList, ok := workOrderData["PicList"].([]interface{}); ok {
		for _, pic := range picList {
			if picUrl, ok := pic.(string); ok && picUrl != "" {
				attachmentURLs = append(attachmentURLs, picUrl)
			}
		}
	}

	// 构建完整的回复内容
	content := reason
	if dealResult != "" && dealResult != reason {
		content = fmt.Sprintf("%s。处理结果：%s", reason, dealResult)
	}

	return &model.WorkOrderCallbackData{
		ProviderWorkOrderID: fmt.Sprintf("%v", complaintNumber),
		Status:              401, // 云通的State通常是401表示已处理
		Content:             content,
		Committer:           "云通客服",
		AttachmentURLs:      attachmentURLs,
		UpdatedAt:           util.NowBeijing(),
	}, nil
}

// findWorkOrder 查找工单
func (f *WorkOrderCallbackForwarder) findWorkOrder(ctx context.Context, provider, providerWorkOrderID string) (*model.WorkOrder, error) {
	return f.workOrderRepo.GetByProviderWorkOrderID(ctx, provider, providerWorkOrderID)
}

// updateWorkOrderStatus 更新工单状态
func (f *WorkOrderCallbackForwarder) updateWorkOrderStatus(ctx context.Context, workOrder *model.WorkOrder, callbackData *model.WorkOrderCallbackData) error {
	if callbackData.Status <= 0 {
		return nil // 留言回调不更新状态
	}

	// 根据供应商状态映射到统一状态
	unifiedStatus, err := f.mapProviderStatusToUnified(workOrder.Provider, callbackData.Status)
	if err != nil {
		f.logger.Warn("状态映射失败", zap.Error(err))
		return nil // 不阻断流程
	}

	f.logger.Info("工单状态映射成功",
		zap.String("provider", workOrder.Provider),
		zap.Int("provider_status", callbackData.Status),
		zap.Int("unified_status", int(unifiedStatus)),
		zap.String("work_order_id", workOrder.ID.String()))

	workOrder.Status = unifiedStatus
	if unifiedStatus == model.WorkOrderStatusCompleted {
		now := util.NowBeijing()
		workOrder.CompletedAt = &now
	}

	return f.workOrderRepo.Update(ctx, workOrder)
}

// mapProviderStatusToUnified 映射供应商状态到统一状态
func (f *WorkOrderCallbackForwarder) mapProviderStatusToUnified(provider string, providerStatus int) (model.WorkOrderStatus, error) {
	ctx := context.Background()
	mapping, err := f.workOrderRepo.GetProviderStatusMapping(ctx, providerStatus, provider)
	if err == nil {
		return model.WorkOrderStatus(mapping.UnifiedStatus), nil
	}

	// ⚠️ 回退逻辑：如数据库未配置映射，使用内置官方文档映射（压缩为三态）
	switch provider {
	case constants.ProviderYuntong:
		switch providerStatus { // 云通工单回调ResultType：0未处理、1无需处理、2驳回、3通过
		case 0:
			return model.WorkOrderStatusPending, nil // 未处理
		case 1:
			return model.WorkOrderStatusProcessing, nil // 无需处理
		case 2, 3:
			return model.WorkOrderStatusCompleted, nil // 驳回/通过
		}
	case constants.ProviderKuaidi100:
		switch providerStatus {
		case 0, 1:
			return model.WorkOrderStatusPending, nil // 未受理/未回复
		case 2:
			return model.WorkOrderStatusProcessing, nil // 已回复
		case 3, 4:
			return model.WorkOrderStatusCompleted, nil // 已完结/已关闭 - 都映射为已完结(状态3)
		}
	case constants.ProviderYida:
		switch providerStatus {
		case constants.YidaTicketStatusPending: // 1 待回复
			return model.WorkOrderStatusPending, nil
		case constants.YidaTicketStatusReplied: // 2 已回复
			return model.WorkOrderStatusProcessing, nil
		case constants.YidaTicketStatusCompleted: // 3 已完结
			return model.WorkOrderStatusCompleted, nil
		}
	case constants.ProviderKuaidiNiao:
		switch providerStatus { // 快递鸟工单：0待处理、1处理中、2已处理
		case 0:
			return model.WorkOrderStatusPending, nil
		case 1:
			return model.WorkOrderStatusProcessing, nil
		case 2:
			return model.WorkOrderStatusCompleted, nil
		}
	}

	// 默认回退到处理中，避免阻断流程
	return model.WorkOrderStatusProcessing, nil
}

// standardizeCallbackData 标准化回调数据
func (f *WorkOrderCallbackForwarder) standardizeCallbackData(
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
	provider string,
) *model.UnifiedWorkOrderCallbackData {
	// 确定事件类型
	eventType := f.determineEventType(callbackData.Status)

	// 获取状态名称
	statusName := f.getStatusName(callbackData.Status, provider)

	// 获取工单类型名称
	workOrderTypeName := f.getWorkOrderTypeName(workOrder.WorkOrderType, provider)

	// 🔥 使用智能订单查找服务查询客户订单号和平台订单号
	customerOrderNo := ""
	platformOrderNo := ""
	if workOrder.OrderNo != nil && *workOrder.OrderNo != "" {
		// 使用智能订单查找服务，提供更准确的查询结果
		if order, err := f.smartOrderFinder.FindOrderByAnyIdentifier(context.Background(), *workOrder.OrderNo, workOrder.UserID); err == nil {
			customerOrderNo = order.CustomerOrderNo
			// 优先使用平台订单号，如果不存在则使用客户订单号
			if order.PlatformOrderNo != "" {
				platformOrderNo = order.PlatformOrderNo
			} else {
				platformOrderNo = order.CustomerOrderNo
			}
			f.logger.Debug("智能订单查找成功",
				zap.String("work_order_id", workOrder.ID.String()),
				zap.String("order_no", *workOrder.OrderNo),
				zap.String("platform_order_no", platformOrderNo),
				zap.String("customer_order_no", customerOrderNo))
		} else {
			f.logger.Info("订单已不存在或已完成，工单独立处理",
				zap.String("work_order_id", workOrder.ID.String()),
				zap.String("order_no", *workOrder.OrderNo),
				zap.Error(err))
		}
	}

	return &model.UnifiedWorkOrderCallbackData{
		EventType:           eventType,
		EventTime:           util.NowBeijing().Unix(),
		Version:             "3.0",
		WorkOrderID:         workOrder.ID.String(),
		ProviderWorkOrderID: callbackData.ProviderWorkOrderID,
		WorkOrderType:       workOrder.WorkOrderType,
		WorkOrderTypeName:   workOrderTypeName,
		OrderNo:             platformOrderNo, // 🔥 修复：使用平台订单号而非供应商单号
		CustomerOrderNo:     customerOrderNo, // 🔥 修复：从订单表查询获取
		TrackingNo:          f.safeStringPtr(workOrder.TrackingNo),
		Provider:            "go-kuaidi", // 🔥 安全修复：隐藏真实供应商，统一显示为平台名称
		Status:              callbackData.Status,
		StatusName:          statusName,
		Content:             f.sanitizeContent(callbackData.Content), // 🔥 清理内容中的供应商信息
		Committer:           "客服",                                    // 🔥 安全修复：统一显示为"客服"，不暴露具体供应商客服
		AttachmentURLs:      callbackData.AttachmentURLs,
		CreatedAt:           workOrder.CreatedAt.Unix(),
		UpdatedAt:           callbackData.UpdatedAt.Unix(),
	}
}

// forwardToUser 转发给用户
func (f *WorkOrderCallbackForwarder) forwardToUser(
	ctx context.Context,
	workOrder *model.WorkOrder,
	standardizedData *model.UnifiedWorkOrderCallbackData,
	requestID string,
) {
	defer func() {
		if r := recover(); r != nil {
			f.logger.Error("转发用户回调发生panic",
				zap.String("request_id", requestID),
				zap.String("work_order_id", workOrder.ID.String()),
				zap.Any("panic", r))
		}
	}()

	// 1. 获取用户回调配置
	config, err := f.callbackRepo.GetUserCallbackConfig(ctx, workOrder.UserID)
	if err != nil || config == nil {
		f.logger.Info("用户未配置回调",
			zap.String("user_id", workOrder.UserID),
			zap.String("work_order_id", workOrder.ID.String()))
		return
	}

	if !config.Enabled || config.CallbackURL == "" {
		f.logger.Info("用户回调未启用或URL为空",
			zap.String("user_id", workOrder.UserID))
		return
	}

	// 2. 检查事件订阅
	if !f.isEventSubscribed(config.SubscribedEvents, standardizedData.EventType) {
		f.logger.Info("用户未订阅此事件",
			zap.String("user_id", workOrder.UserID),
			zap.String("event_type", standardizedData.EventType))
		return
	}

	// 3. 创建转发记录
	record := &model.WorkOrderForwardRecord{
		ID:          uuid.New(),
		WorkOrderID: workOrder.ID,
		UserID:      workOrder.UserID,
		CallbackURL: config.CallbackURL,
		EventType:   standardizedData.EventType,
		Status:      "pending",
		HTTPStatus:  200, // 🔥 修复：设置默认HTTP状态码，避免约束错误
		CreatedAt:   util.NowBeijing(),
		UpdatedAt:   util.NowBeijing(),
	}

	// 序列化请求数据
	requestData, err := json.Marshal(standardizedData)
	if err != nil {
		f.logger.Error("序列化回调数据失败",
			zap.Error(err),
			zap.Any("standardized_data", standardizedData))
		return
	}

	// 🔥 修复：验证JSON数据有效性
	if !json.Valid(requestData) {
		f.logger.Error("生成的JSON数据无效",
			zap.String("json_data", string(requestData)))
		return
	}

	// 🔥 修复：转换为json.RawMessage类型
	record.RequestData = json.RawMessage(requestData)

	// 🔥 调试：打印序列化后的JSON数据
	f.logger.Info("序列化后的JSON数据",
		zap.String("json_data", string(requestData)),
		zap.Int("data_length", len(requestData)),
		zap.Bool("json_valid", json.Valid(requestData)))

	// 4. 保存转发记录
	if err := f.callbackRepo.SaveWorkOrderForwardRecord(ctx, record); err != nil {
		f.logger.Error("保存转发记录失败",
			zap.Error(err),
			zap.String("record_id", record.ID.String()),
			zap.String("user_id", record.UserID),
			zap.String("event_type", record.EventType))
		// 🔥 暂时不因为保存失败而中断转发，继续执行转发逻辑
		// return
	} else {
		f.logger.Info("转发记录保存成功",
			zap.String("record_id", record.ID.String()),
			zap.String("user_id", record.UserID))
	}

	// 5. 发送HTTP请求
	f.sendCallbackRequest(ctx, config, standardizedData, record)
}

// sendCallbackRequest 发送回调请求
func (f *WorkOrderCallbackForwarder) sendCallbackRequest(
	ctx context.Context,
	config *model.UserCallbackConfig,
	data *model.UnifiedWorkOrderCallbackData,
	record *model.WorkOrderForwardRecord,
) {
	// 生成签名
	signature := f.generateSignature(data, config.CallbackSecret)

	// 构建请求
	requestData, _ := json.Marshal(data)
	req, err := http.NewRequestWithContext(ctx, "POST", config.CallbackURL, bytes.NewBuffer(requestData))
	if err != nil {
		f.updateForwardRecord(ctx, record, "failed", 0, nil, err.Error())
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "go-kuaidi-callback/3.0")
	req.Header.Set("X-Signature", signature)
	req.Header.Set("X-Event-Type", data.EventType)
	req.Header.Set("X-Timestamp", strconv.FormatInt(data.EventTime, 10))

	// 发送请求
	now := util.NowBeijing()
	record.RequestAt = &now

	resp, err := f.httpClient.Do(req)
	if err != nil {
		f.updateForwardRecord(ctx, record, "failed", 0, nil, err.Error())
		return
	}
	defer resp.Body.Close()

	// 读取响应
	responseData := make([]byte, 0, 1024)
	buffer := make([]byte, 1024)
	for {
		n, err := resp.Body.Read(buffer)
		if n > 0 {
			responseData = append(responseData, buffer[:n]...)
		}
		if err != nil {
			break
		}
	}

	// 更新记录
	status := "success"
	errorMsg := ""
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		status = "failed"
		errorMsg = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(responseData))
	}

	f.updateForwardRecord(ctx, record, status, resp.StatusCode, responseData, errorMsg)

	f.logger.Info("工单回调转发完成",
		zap.String("user_id", record.UserID),
		zap.String("callback_url", config.CallbackURL),
		zap.String("event_type", data.EventType),
		zap.Int("http_status", resp.StatusCode),
		zap.String("status", status))
}

// updateForwardRecord 更新转发记录
func (f *WorkOrderCallbackForwarder) updateForwardRecord(
	ctx context.Context,
	record *model.WorkOrderForwardRecord,
	status string,
	httpStatus int,
	responseData []byte,
	errorMsg string,
) {
	now := util.NowBeijing()
	record.Status = status
	record.HTTPStatus = httpStatus
	record.ErrorMessage = errorMsg
	record.ResponseAt = &now
	record.UpdatedAt = now

	if len(responseData) > 0 {
		// 🔥 修复：验证响应数据是否为有效JSON
		if json.Valid(responseData) {
			record.ResponseData = json.RawMessage(responseData)
		} else {
			// 如果不是有效JSON，包装成JSON字符串
			jsonStr, _ := json.Marshal(string(responseData))
			record.ResponseData = json.RawMessage(jsonStr)
		}

		f.logger.Debug("响应数据处理",
			zap.String("record_id", record.ID.String()),
			zap.Int("response_size", len(responseData)),
			zap.Bool("is_valid_json", json.Valid(responseData)))
	}

	if err := f.callbackRepo.UpdateWorkOrderForwardRecord(ctx, record); err != nil {
		f.logger.Error("更新转发记录失败",
			zap.Error(err),
			zap.String("record_id", record.ID.String()),
			zap.String("status", status),
			zap.Int("http_status", httpStatus))
	} else {
		f.logger.Debug("转发记录更新成功",
			zap.String("record_id", record.ID.String()),
			zap.String("status", status))
	}
}

// 辅助方法
func (f *WorkOrderCallbackForwarder) determineEventType(status int) string {
	switch status {
	case 3:
		return model.WorkOrderEventCompleted
	case 2:
		return model.WorkOrderEventReplied
	case 0:
		return model.WorkOrderEventReplied // 留言也算回复
	case 401: // 云通的401状态表示已处理/已回复
		return model.WorkOrderEventReplied
	default:
		return model.WorkOrderEventUpdated
	}
}

func (f *WorkOrderCallbackForwarder) getStatusName(status int, provider string) string {
	statusNames := map[int]string{
		0: "留言",
		1: "处理中",
		2: "已回复",
		3: "已完结",
	}
	if name, exists := statusNames[status]; exists {
		return name
	}
	return fmt.Sprintf("状态%d", status)
}

func (f *WorkOrderCallbackForwarder) getWorkOrderTypeName(workOrderType int, provider string) string {
	// 从数据库获取类型名称
	ctx := context.Background()
	mapping, err := f.workOrderRepo.GetTypeMapping(ctx, workOrderType, provider)
	if err == nil && mapping.UnifiedName != "" {
		return mapping.UnifiedName
	}
	return fmt.Sprintf("类型%d", workOrderType)
}

func (f *WorkOrderCallbackForwarder) isEventSubscribed(subscribedEvents []string, eventType string) bool {
	// 规则：
	// 1. subscribedEvents 为空 → 默认订阅全部
	// 2. 包含 "*" → 订阅全部
	// 3. 精确匹配 eventType，兼容下划线与点
	// 4. 旧版别名映射 (ticket_replied → workorder.replied 等)
	// 5. 以 "xxx.*" 结尾 → 前缀匹配

	if len(subscribedEvents) == 0 {
		return true // 默认订阅所有事件
	}

	// 别名映射
	aliasMap := map[string]string{
		"ticket.replied":   model.WorkOrderEventReplied,
		"ticket_replied":   model.WorkOrderEventReplied,
		"ticket.completed": model.WorkOrderEventCompleted,
		"ticket_completed": model.WorkOrderEventCompleted,
	}

	for _, rawEvt := range subscribedEvents {
		evt := strings.TrimSpace(rawEvt)
		if evt == "*" {
			return true
		}

		// 精确匹配 (点 / 下划线互通)
		if evt == eventType || strings.ReplaceAll(evt, "_", ".") == eventType || strings.ReplaceAll(eventType, ".", "_") == evt {
			return true
		}

		// 别名映射检查
		normalized := strings.ReplaceAll(evt, "_", ".")
		if mapped, ok := aliasMap[normalized]; ok && mapped == eventType {
			return true
		}

		// 前缀通配符 "xxx.*"
		if strings.HasSuffix(evt, ".*") {
			prefix := strings.TrimSuffix(evt, ".*")
			if strings.HasPrefix(eventType, prefix) {
				return true
			}
		}
	}

	return false
}

func (f *WorkOrderCallbackForwarder) generateSignature(data *model.UnifiedWorkOrderCallbackData, secret string) string {
	// 构建签名字符串
	signStr := fmt.Sprintf("%s%d%s%s", data.EventType, data.EventTime, data.WorkOrderID, secret)
	hash := md5.Sum([]byte(signStr))
	return fmt.Sprintf("%x", hash)
}

func (f *WorkOrderCallbackForwarder) safeStringPtr(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// sanitizeContent 清理内容中的供应商信息，保护商业机密
func (f *WorkOrderCallbackForwarder) sanitizeContent(content string) string {
	// 移除可能泄露供应商身份的关键词
	supplierKeywords := []string{
		"【快递100】", "快递100", "kuaidi100",
		"【易达】", "易达", "yida", "易达178",
		"【云通】", "云通", "yuntong", "韵达",
		"快递100客服", "易达客服", "云通客服",
	}

	sanitized := content
	for _, keyword := range supplierKeywords {
		// 替换为通用的平台表述
		sanitized = strings.ReplaceAll(sanitized, keyword, "")
	}

	// 清理多余的空格和标点
	sanitized = strings.TrimSpace(sanitized)
	// 移除开头的特殊字符
	sanitized = strings.TrimLeft(sanitized, "：:：。，,")
	sanitized = strings.TrimSpace(sanitized)

	return sanitized
}

func (f *WorkOrderCallbackForwarder) buildSuccessResponse(provider string) *model.CallbackResponse {
	switch provider {
	case "kuaidi100":
		return &model.CallbackResponse{
			Success:      true,
			Code:         "200",
			Message:      "success",
			DirectReturn: true,
			Data: map[string]interface{}{
				"code":    200,
				"success": true,
				"message": "success",
				"time":    0,
			},
		}
	case "yida":
		return &model.CallbackResponse{
			Success:      true,
			Code:         "200",
			Message:      "接收成功",
			DirectReturn: true,
			Data: map[string]interface{}{
				"success": true,
				"code":    "200",
				"msg":     "接收成功",
			},
		}
	case "yuntong":
		return &model.CallbackResponse{
			Success:      true,
			Code:         "200",
			Message:      "成功",
			DirectReturn: true,
			Data: map[string]interface{}{
				"EBusinessID": "",
				"Success":     true,
				"Reason":      "成功",
				"UpdateTime":  util.NowBeijing().Format("2006-01-02 15:04:05"),
			},
		}
	default:
		return &model.CallbackResponse{
			Success: true,
			Code:    "200",
			Message: "处理成功",
		}
	}
}

func (f *WorkOrderCallbackForwarder) buildErrorResponse(provider, message string) *model.CallbackResponse {
	switch provider {
	case "kuaidi100":
		return &model.CallbackResponse{
			Success: false,
			Code:    "400",
			Message: message,
			Data: map[string]interface{}{
				"code":    400,
				"success": false,
				"message": message,
			},
		}
	case "yida":
		return &model.CallbackResponse{
			Success: false,
			Code:    "400",
			Message: message,
			Data: map[string]interface{}{
				"success": false,
				"code":    "400",
				"msg":     message,
			},
		}
	case "yuntong":
		return &model.CallbackResponse{
			Success: false,
			Code:    "400",
			Message: message,
			Data: map[string]interface{}{
				"EBusinessID": "",
				"Success":     false,
				"Reason":      message,
				"UpdateTime":  util.NowBeijing().Format("2006-01-02 15:04:05"),
			},
		}
	default:
		return &model.CallbackResponse{
			Success: false,
			Code:    "400",
			Message: message,
		}
	}
}

// splitString 分割字符串
func splitString(s, sep string) []string {
	if s == "" {
		return []string{}
	}
	result := make([]string, 0)
	current := ""
	for _, char := range s {
		if string(char) == sep {
			if current != "" {
				result = append(result, trimString(current))
				current = ""
			}
		} else {
			current += string(char)
		}
	}
	if current != "" {
		result = append(result, trimString(current))
	}
	return result
}

func trimString(s string) string {
	// 简化的trim实现
	start := 0
	end := len(s)
	for start < end && (s[start] == ' ' || s[start] == '\t' || s[start] == '\n') {
		start++
	}
	for end > start && (s[end-1] == ' ' || s[end-1] == '\t' || s[end-1] == '\n') {
		end--
	}
	return s[start:end]
}

// 🔥 新增：内部工单处理逻辑
func (f *WorkOrderCallbackForwarder) processInternalWorkOrderLogic(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
	provider string,
	requestID string,
) error {
	f.logger.Info("开始内部工单处理",
		zap.String("request_id", requestID),
		zap.String("work_order_id", workOrder.ID.String()),
		zap.String("provider", provider))

	// 1. 更新工单状态（如果有状态变更）
	if callbackData.Status > 0 {
		if err := f.updateWorkOrderStatus(ctx, workOrder, callbackData); err != nil {
			f.logger.Error("更新工单状态失败",
				zap.String("request_id", requestID),
				zap.Error(err))
			return fmt.Errorf("更新工单状态失败: %w", err)
		}
	}

	// 2. 保存回复内容（如果有回复）
	if callbackData.Content != "" {
		if err := f.saveWorkOrderReply(ctx, workOrder, callbackData, provider); err != nil {
			f.logger.Error("保存工单回复失败",
				zap.String("request_id", requestID),
				zap.Error(err))
			return fmt.Errorf("保存工单回复失败: %w", err)
		}
	}

	// 3. 处理附件（如果有附件）
	if len(callbackData.AttachmentURLs) > 0 {
		if err := f.processWorkOrderAttachments(ctx, workOrder, callbackData.AttachmentURLs); err != nil {
			f.logger.Error("处理工单附件失败",
				zap.String("request_id", requestID),
				zap.Error(err))
			// 附件处理失败不阻断主流程
		}
	}

	// 4. 更新工单最后活动时间
	if err := f.updateWorkOrderLastActivity(ctx, workOrder.ID, callbackData.UpdatedAt); err != nil {
		f.logger.Error("更新工单最后活动时间失败",
			zap.String("request_id", requestID),
			zap.Error(err))
		// 时间更新失败不阻断主流程
	}

	// 5. 触发工单业务逻辑
	if err := f.triggerWorkOrderBusinessLogic(ctx, workOrder, callbackData, provider); err != nil {
		f.logger.Error("触发工单业务逻辑失败",
			zap.String("request_id", requestID),
			zap.Error(err))
		// 业务逻辑失败不阻断主流程
	}

	f.logger.Info("内部工单处理完成",
		zap.String("request_id", requestID),
		zap.String("work_order_id", workOrder.ID.String()))

	return nil
}

// 🔥 新增：保存工单回复
func (f *WorkOrderCallbackForwarder) saveWorkOrderReply(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
	provider string,
) error {
	reply := &model.WorkOrderReply{
		ID:          uuid.New(),
		WorkOrderID: workOrder.ID,
		ReplyType:   model.ReplyTypeProvider, // 标记为供应商回复
		Committer:   &callbackData.Committer,
		Content:     callbackData.Content,
		CreatedAt:   callbackData.UpdatedAt,
	}

	// 将附件URL和其他信息序列化为RawData
	if len(callbackData.AttachmentURLs) > 0 || provider != "" {
		rawDataMap := map[string]interface{}{
			"provider":        provider,
			"attachment_urls": callbackData.AttachmentURLs,
		}
		if rawDataJSON, err := json.Marshal(rawDataMap); err == nil {
			rawDataStr := string(rawDataJSON)
			reply.RawData = &rawDataStr
		}
	}

	return f.workOrderRepo.CreateReply(ctx, reply)
}

// 🔥 新增：处理工单附件
func (f *WorkOrderCallbackForwarder) processWorkOrderAttachments(
	ctx context.Context,
	workOrder *model.WorkOrder,
	attachmentURLs []string,
) error {
	for _, url := range attachmentURLs {
		if url == "" {
			continue
		}

		attachment := &model.WorkOrderAttachment{
			ID:          uuid.New(),
			WorkOrderID: &workOrder.ID,
			FileName:    f.extractFileNameFromURL(url),
			FileURL:     url,
			FileType:    nil,                   // 使用FileType而不是MimeType
			FileSize:    nil,                   // 供应商回调通常不提供文件大小
			UploadType:  model.UploadTypeReply, // 标记为回复时上传
			CreatedAt:   util.NowBeijing(),
		}

		// 设置文件类型
		mimeType := f.guessMimeTypeFromURL(url)
		attachment.FileType = &mimeType

		if err := f.workOrderRepo.CreateAttachment(ctx, attachment); err != nil {
			f.logger.Error("保存工单附件失败",
				zap.String("work_order_id", workOrder.ID.String()),
				zap.String("attachment_url", url),
				zap.Error(err))
			// 继续处理其他附件
		}
	}

	return nil
}

// 🔥 新增：更新工单最后活动时间
func (f *WorkOrderCallbackForwarder) updateWorkOrderLastActivity(
	ctx context.Context,
	workOrderID uuid.UUID,
	activityTime time.Time,
) error {
	// 通过更新工单的updated_at字段来记录最后活动时间
	// 我们可以通过Update方法来实现，但需要先获取工单
	workOrder, err := f.workOrderRepo.GetByID(ctx, workOrderID)
	if err != nil {
		return fmt.Errorf("获取工单失败: %w", err)
	}

	workOrder.UpdatedAt = activityTime
	return f.workOrderRepo.Update(ctx, workOrder)
}

// 🔥 新增：触发工单业务逻辑
func (f *WorkOrderCallbackForwarder) triggerWorkOrderBusinessLogic(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
	provider string,
) error {
	// 根据工单状态和回调内容触发相应的业务逻辑
	switch {
	case callbackData.Status == 2: // 工单已解决
		return f.handleWorkOrderResolved(ctx, workOrder, callbackData)
	case callbackData.Status == 3: // 工单已关闭
		return f.handleWorkOrderClosed(ctx, workOrder, callbackData)
	case callbackData.Content != "": // 有新回复
		return f.handleWorkOrderReplied(ctx, workOrder, callbackData)
	default:
		// 其他情况，记录日志即可
		f.logger.Info("工单回调无需特殊业务逻辑处理",
			zap.String("work_order_id", workOrder.ID.String()),
			zap.Int("status", callbackData.Status),
			zap.String("provider", provider))
		return nil
	}
}

// 🔥 新增：处理工单已解决
func (f *WorkOrderCallbackForwarder) handleWorkOrderResolved(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
) error {
	f.logger.Info("工单已解决，触发解决逻辑",
		zap.String("work_order_id", workOrder.ID.String()),
		zap.String("solution", callbackData.Content))

	// 1. 标记工单为已解决
	// 2. 记录解决时间
	// 3. 可能的满意度调查触发
	// 4. 统计数据更新

	return nil
}

// 🔥 新增：处理工单已关闭
func (f *WorkOrderCallbackForwarder) handleWorkOrderClosed(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
) error {
	f.logger.Info("工单已关闭，触发关闭逻辑",
		zap.String("work_order_id", workOrder.ID.String()))

	// 1. 标记工单为已关闭
	// 2. 记录关闭时间
	// 3. 清理相关资源
	// 4. 统计数据更新

	return nil
}

// 🔥 新增：处理工单有新回复
func (f *WorkOrderCallbackForwarder) handleWorkOrderReplied(
	ctx context.Context,
	workOrder *model.WorkOrder,
	callbackData *model.WorkOrderCallbackData,
) error {
	f.logger.Info("工单有新回复，触发回复逻辑",
		zap.String("work_order_id", workOrder.ID.String()),
		zap.String("committer", callbackData.Committer))

	// 1. 更新工单为有新回复状态
	// 2. 可能的用户通知
	// 3. 统计数据更新

	return nil
}

// 🔥 新增：从URL提取文件名
func (f *WorkOrderCallbackForwarder) extractFileNameFromURL(url string) string {
	// 简单的文件名提取逻辑
	parts := strings.Split(url, "/")
	if len(parts) > 0 {
		fileName := parts[len(parts)-1]
		// 移除查询参数
		if idx := strings.Index(fileName, "?"); idx > 0 {
			fileName = fileName[:idx]
		}
		return fileName
	}
	return "attachment"
}

// 🔥 新增：从URL猜测MIME类型
func (f *WorkOrderCallbackForwarder) guessMimeTypeFromURL(url string) string {
	fileName := f.extractFileNameFromURL(url)
	ext := strings.ToLower(filepath.Ext(fileName))

	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".pdf":
		return "application/pdf"
	case ".doc":
		return "application/msword"
	case ".docx":
		return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
	case ".txt":
		return "text/plain"
	default:
		return "application/octet-stream"
	}
}

// saveUnifiedCallbackRecord 保存统一回调记录
func (f *WorkOrderCallbackForwarder) saveUnifiedCallbackRecord(
	ctx context.Context,
	workOrder *model.WorkOrder,
	parsedData *model.WorkOrderCallbackData,
	standardizedData *model.UnifiedWorkOrderCallbackData,
	provider string,
	requestID string,
) error {
	// 序列化原始数据
	rawData, err := json.Marshal(parsedData)
	if err != nil {
		return fmt.Errorf("序列化原始数据失败: %w", err)
	}

	// 序列化标准化数据
	standardizedDataBytes, err := json.Marshal(standardizedData)
	if err != nil {
		return fmt.Errorf("序列化标准化数据失败: %w", err)
	}

	// 创建统一回调记录
	record := &model.UnifiedCallbackRecord{
		ID:               uuid.New(),
		Provider:         provider,
		CallbackType:     "workorder",
		OrderNo:          standardizedData.OrderNo,
		CustomerOrderNo:  standardizedData.CustomerOrderNo,
		TrackingNo:       standardizedData.TrackingNo,
		UserID:           workOrder.UserID,
		RawData:          json.RawMessage(rawData),
		StandardizedData: json.RawMessage(standardizedDataBytes),
		EventType:        standardizedData.EventType,
		InternalStatus:   model.CallbackStatusSuccess,
		ExternalStatus:   model.CallbackStatusPending,
		ReceivedAt:       util.NowBeijing(),
		CreatedAt:        util.NowBeijing(),
		UpdatedAt:        util.NowBeijing(),
	}

	// 保存到数据库
	return f.callbackRepo.SaveCallbackRecord(ctx, record)
}
