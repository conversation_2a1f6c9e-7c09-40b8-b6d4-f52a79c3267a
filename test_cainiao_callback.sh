#!/bin/bash

# 菜鸟回调测试脚本
echo "🧪 测试菜鸟计费重量修复..."

# 原始回调数据
RAW_DATA="data_digest=2DvYBiIJmSRNkZSzCRViHg%3D%3D&from_code=tdtradebusiness&msg_type=GUOGUO_PUSH_ORDER_EVENT&partner_code=20df7df0fa15494c801b249a8b798879&msg_id=18348890601677818&logistics_interface=%7B%22externalOrder%22%3A%7B%22externalBizIdList%22%3A%5B%22GK20250808000002665%22%5D%2C%22orderId%22%3A%2218348890601677818%22%2C%22orderStatusDesc%22%3A%22%E5%B7%B2%E5%AE%8C%E7%BB%93%22%2C%22orderStatusCode%22%3A%2240%22%7D%2C%22orderEvent%22%3A%7B%22eventDesc%22%3A%22%E5%B0%8F%E4%BB%B6%E5%91%98%E6%A0%B8%E4%BB%B7%E6%88%90%E5%8A%9F%22%2C%22eventData%22%3A%7B%22insuranceValue%22%3A%220%22%2C%22itemId%22%3A%223000000080%22%2C%22courierAdjustFee%22%3A%220%22%2C%22lpCode%22%3A%22LP00752862057787%22%2C%22insurancePrice%22%3A%220%22%2C%22weight%22%3A%224000%22%2C%22basePrice%22%3A%221180%22%2C%22packageFee%22%3A%220%22%2C%22ladderCount%22%3A%221%22%7D%2C%22eventType%22%3A%22COURIER_CHECK_BILL_SUCCESS%22%7D%7D"

echo "📊 关键计费信息:"
echo "  weight: 4000 (克) = 4.0 kg"
echo "  basePrice: 1180 (分) = 11.80 元"
echo "  eventType: COURIER_CHECK_BILL_SUCCESS"

echo ""
echo "🚀 发送回调请求到本地服务..."

# 发送POST请求
curl -X POST \
  http://localhost:8081/api/v1/callbacks/cainiao \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "$RAW_DATA" \
  -v

echo ""
echo "✅ 回调请求已发送"
