#!/bin/bash

# 菜鸟重量更新测试脚本
echo "🧪 测试菜鸟重量更新功能（幂等性保护修复）..."

# 🔥 修改后的回调数据：将重量从4000克改为3900克（3.9kg）
# 费用保持不变（1180分 = 11.80元），测试重量更新是否能正常工作
RAW_DATA="data_digest=2DvYBiIJmSRNkZSzCRViHg%3D%3D&from_code=tdtradebusiness&msg_type=GUOGUO_PUSH_ORDER_EVENT&partner_code=20df7df0fa15494c801b249a8b798879&msg_id=18348890601677818&logistics_interface=%7B%22externalOrder%22%3A%7B%22externalBizIdList%22%3A%5B%22GK20250808000002665%22%5D%2C%22orderId%22%3A%2218348890601677818%22%2C%22orderStatusDesc%22%3A%22%E5%B7%B2%E5%AE%8C%E7%BB%93%22%2C%22orderStatusCode%22%3A%2240%22%7D%2C%22orderEvent%22%3A%7B%22eventDesc%22%3A%22%E5%B0%8F%E4%BB%B6%E5%91%98%E6%A0%B8%E4%BB%B7%E6%88%90%E5%8A%9F%22%2C%22eventData%22%3A%7B%22insuranceValue%22%3A%220%22%2C%22itemId%22%3A%223000000080%22%2C%22courierAdjustFee%22%3A%220%22%2C%22lpCode%22%3A%22LP00752862057787%22%2C%22insurancePrice%22%3A%220%22%2C%22weight%22%3A%223900%22%2C%22basePrice%22%3A%221180%22%2C%22packageFee%22%3A%220%22%2C%22ladderCount%22%3A%221%22%7D%2C%22eventType%22%3A%22COURIER_CHECK_BILL_SUCCESS%22%7D%7D"

echo "📊 修改后的计费信息:"
echo "  weight: 3900 (克) = 3.9 kg  ⬅️ 从4.0kg改为3.9kg"
echo "  basePrice: 1180 (分) = 11.80 元  ⬅️ 费用保持不变"
echo "  eventType: COURIER_CHECK_BILL_SUCCESS"
echo ""
echo "🎯 测试目标:"
echo "  1. 费用幂等性检查应该检测到重复费用（11.80元）"
echo "  2. 重量更新检查应该检测到重量变化（4.0kg → 3.9kg）"
echo "  3. 系统应该只更新重量信息，不处理费用"
echo "  4. 最终数据库中应该显示：actual_weight=3.9, charged_weight=3.9"

echo ""
echo "🚀 发送重量更新回调请求到本地服务..."

# 发送POST请求
curl -X POST \
  http://localhost:8081/api/v1/callbacks/cainiao \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "$RAW_DATA" \
  -v

echo ""
echo "✅ 重量更新回调请求已发送"
echo ""
echo "🔍 请检查服务器日志，应该看到："
echo "  - '🔄 检测到重复费用回调但需要更新重量信息'"
echo "  - '✅ 成功更新重量信息（跳过费用处理）'"
