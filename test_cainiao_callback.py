#!/usr/bin/env python3
import urllib.parse
import json
import requests

# 原始回调数据
raw_content = "data_digest=2DvYBiIJmSRNkZSzCRViHg%3D%3D&from_code=tdtradebusiness&msg_type=GUOGUO_PUSH_ORDER_EVENT&partner_code=20df7df0fa15494c801b249a8b798879&msg_id=18348890601677818&logistics_interface=%7B%22externalOrder%22%3A%7B%22externalBizIdList%22%3A%5B%22GK20250808000002665%22%5D%2C%22orderId%22%3A%2218348890601677818%22%2C%22orderStatusDesc%22%3A%22%E5%B7%B2%E5%AE%8C%E7%BB%93%22%2C%22orderStatusCode%22%3A%2240%22%7D%2C%22orderEvent%22%3A%7B%22eventDesc%22%3A%22%E5%B0%8F%E4%BB%B6%E5%91%98%E6%A0%B8%E4%BB%B7%E6%88%90%E5%8A%9F%22%2C%22eventData%22%3A%7B%22insuranceValue%22%3A%220%22%2C%22itemId%22%3A%223000000080%22%2C%22courierAdjustFee%22%3A%220%22%2C%22lpCode%22%3A%22LP00752862057787%22%2C%22insurancePrice%22%3A%220%22%2C%22weight%22%3A%224000%22%2C%22basePrice%22%3A%221180%22%2C%22packageFee%22%3A%220%22%2C%22ladderCount%22%3A%221%22%7D%2C%22eventType%22%3A%22COURIER_CHECK_BILL_SUCCESS%22%7D%7D"

# 解析URL编码数据
parsed_data = urllib.parse.parse_qs(raw_content)
print("解析后的数据:")
for key, value in parsed_data.items():
    print(f"{key}: {value[0]}")

# 解析logistics_interface JSON
logistics_interface = json.loads(parsed_data['logistics_interface'][0])
print("\nlogistics_interface JSON:")
print(json.dumps(logistics_interface, indent=2, ensure_ascii=False))

# 提取关键信息
event_data = logistics_interface['orderEvent']['eventData']
print(f"\n关键计费信息:")
print(f"weight: {event_data['weight']} (克)")
print(f"basePrice: {event_data['basePrice']} (分)")
print(f"weight转换为kg: {int(event_data['weight']) / 1000}")
print(f"basePrice转换为元: {int(event_data['basePrice']) / 100}")

# 发送测试请求到本地服务
print("\n发送测试请求到本地服务...")
try:
    response = requests.post(
        'http://localhost:8081/api/v1/callback/cainiao',
        data=raw_content,
        headers={
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        timeout=10
    )
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
except Exception as e:
    print(f"请求失败: {e}")
