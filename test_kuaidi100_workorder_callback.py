#!/usr/bin/env python3
"""
快递100工单回调模拟推送测试脚本
基于真实回调数据格式
"""

import requests
import json
import time

# 服务端点
WORKORDER_CALLBACK_URL = "http://localhost:8081/api/v1/callbacks/workorders/kuaidi100"

def test_workorder_status_callback():
    """测试工单状态回调"""
    print("🚀 测试工单状态回调")
    print(f"🔗 目标URL: {WORKORDER_CALLBACK_URL}")
    print("-" * 50)
    
    # 基于真实数据的工单状态回调
    callback_payload = {
        "workorderId": 5608942,
        "status": 3,  # 3=已完成
        "result": "重量无误",
        "callbackUrl": "http://localhost:8081/api/v1/callbacks/workorders/kuaidi100"
    }
    
    try:
        print("📤 发送工单状态回调...")
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': '<PERSON>aidi100-WorkOrder-Bot/1.0'
        }
        
        response = requests.post(
            WORKORDER_CALLBACK_URL,
            json=callback_payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📨 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 工单状态回调处理成功")
        else:
            print(f"❌ 工单状态回调处理失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def test_workorder_content_callback():
    """测试工单内容回复回调"""
    print("\n🚀 测试工单内容回复回调")
    print(f"🔗 目标URL: {WORKORDER_CALLBACK_URL}")
    print("-" * 50)
    
    # 基于真实数据的工单内容回调
    callback_payload = {
        "workorderId": 5608942,
        "committer": "客服小王",
        "content": "核实重量为1.1千克，已与客户沟通解释。运单号312864095110307已正常派送。",
        "lastModified": int(time.time() * 1000),  # 当前时间戳
        "attach": None,
        "callbackUrl": None
    }
    
    try:
        print("📤 发送工单内容回调...")
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Kuaidi100-WorkOrder-Bot/1.0'
        }
        
        response = requests.post(
            WORKORDER_CALLBACK_URL,
            json=callback_payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📨 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 工单内容回调处理成功")
        else:
            print(f"❌ 工单内容回调处理失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")

def test_different_workorder_statuses():
    """测试不同工单状态的回调"""
    print("\n🧪 测试不同工单状态的回调...")
    
    statuses = [
        (1, "已创建"),
        (2, "处理中"), 
        (3, "已完成")
        # 注意：status 4 已从测试中移除，因为它不是快递100的标准状态
    ]
    
    for status, desc in statuses:
        print(f"\n📊 测试状态: {status} ({desc})")
        
        test_payload = {
            "workorderId": 5608943,  # 使用不同的工单ID
            "status": status,
            "result": f"工单状态测试: {desc}",
            "callbackUrl": "http://localhost:8081/api/v1/callbacks/workorders/kuaidi100"
        }
        
        try:
            response = requests.post(
                WORKORDER_CALLBACK_URL,
                json=test_payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            if response.status_code != 200:
                print(f"   响应: {response.text[:200]}")
                
        except Exception as e:
            print(f"   ❌ 错误: {e}")
            
        time.sleep(0.5)  # 避免过于频繁的请求

def test_workorder_with_attachment():
    """测试带附件的工单回调"""
    print("\n🚀 测试带附件的工单回调")
    print("-" * 50)
    
    callback_payload = {
        "workorderId": 5608944,
        "committer": "快递员张三",
        "content": "重量确认完成，附上称重照片。运单号312864095110307重量核实无误。",
        "lastModified": int(time.time() * 1000),
        "attach": "https://example.com/weight_photo.jpg",  # 模拟附件URL
        "callbackUrl": None
    }
    
    try:
        response = requests.post(
            WORKORDER_CALLBACK_URL,
            json=callback_payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"📨 响应状态码: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 带附件工单回调处理成功")
        else:
            print(f"❌ 带附件工单回调处理失败")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("快递100工单回调模拟测试")
    print("基于运单号312864095110307的真实回调数据格式")
    print("=" * 60)
    
    # 测试工单状态回调
    test_workorder_status_callback()
    
    # 测试工单内容回调
    test_workorder_content_callback()
    
    # 测试不同状态
    test_different_workorder_statuses()
    
    # 测试带附件的回调
    test_workorder_with_attachment()
    
    print("\n✅ 工单回调测试完成")